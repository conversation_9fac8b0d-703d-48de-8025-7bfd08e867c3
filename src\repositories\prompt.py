import asyncio

from bson import ObjectId
from pymongo import DESCENDING

from src.models.prompt import InsertPromptInput, Prompt, UpdatePromptInput
from src.models.shared import Pagination, PaginationResponse, PaginationResult
from src.util.mongo import db


class PromptRepository:
    def __init__(self):
        self.collection = db.get_collection("prompts")

    def _to_model(self, prompt_dict: dict) -> Prompt:
        return Prompt(
            id=str(prompt_dict.get("_id")),
            project_id=str(prompt_dict.get("project_id")),
            user_id=str(prompt_dict.get("user_id")),
            name=prompt_dict.get("name"),
            text=prompt_dict.get("text"),
            created_at=prompt_dict.get("created_at"),
        )

    async def find_one(
        self,
        prompt_id: str | None = None,
        project_id: str | None = None,
        user_id: str | None = None,
    ) -> Prompt | None:
        filter = {}
        if prompt_id:
            filter["_id"] = ObjectId(prompt_id)
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        prompt_dict = await self.collection.find_one(filter=filter)
        return self._to_model(prompt_dict) if prompt_dict else None

    async def find_many(
        self,
        prompt_ids: list[str] | None = None,
        project_id: str | None = None,
        user_id: str | None = None,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[Prompt]:
        filter = {}
        if prompt_ids:
            filter["_id"] = {"$in": [ObjectId(prompt_id) for prompt_id in prompt_ids]}
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        project_dicts = await self.collection.find(filter=filter, sort=sort).to_list()
        return [self._to_model(project_dict) for project_dict in project_dicts]

    async def paginate(
        self,
        project_id: str | None = None,
        user_id: str | None = None,
        name: str | None = None,
        page_size: int = 10,
        page_number: int = 1,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> PaginationResponse[Prompt]:
        filter = {}
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        if name:
            filter["name"] = {"$regex": name, "$options": "i"}

        pagination = Pagination(page_size=page_size, page_number=page_number)

        (total, prompt_dicts) = await asyncio.gather(
            self.collection.count_documents(filter=filter),
            self.collection.find(
                filter=filter,
                limit=pagination.limit(),
                skip=pagination.skip(),
                sort=sort,
            ).to_list(),
        )

        return PaginationResponse(
            data=[self._to_model(prompt_dict) for prompt_dict in prompt_dicts],
            pagination=PaginationResult(
                total=total,
                page_size=page_size,
                page_number=page_number,
            ),
        )

    async def insert_one(self, input: InsertPromptInput) -> Prompt:
        result = await self.collection.insert_one(input.to_dict())
        return await self.find_one(prompt_id=result.inserted_id)

    async def update_one(
        self,
        prompt_id: str,
        input: UpdatePromptInput,
        project_id: str | None = None,
        user_id: str | None = None,
    ) -> Prompt | None:
        filter = {"_id": ObjectId(prompt_id)}
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        update = {}
        if input.name:
            update["name"] = input.name
        if input.text:
            update["text"] = input.text
        await self.collection.update_one(
            filter=filter,
            update={"$set": update},
        )
        return await self.find_one(prompt_id=prompt_id)

    async def delete_one(
        self,
        prompt_id: str,
        project_id: str | None = None,
        user_id: str | None = None,
    ) -> Prompt | None:
        prompt = await self.find_one(
            prompt_id=prompt_id,
            project_id=project_id,
            user_id=user_id,
        )
        if not prompt:
            return None
        filter = {}
        if prompt_id:
            filter["_id"] = ObjectId(prompt_id)
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        await self.collection.delete_one(filter=filter)
        return prompt

    async def delete_many(
        self,
        prompt_ids: list[str] | None = None,
        project_id: str | None = None,
        user_id: str | None = None,
    ) -> list[Prompt]:
        prompts = await self.find_many(
            prompt_ids=prompt_ids,
            project_id=project_id,
            user_id=user_id,
        )
        if not prompts:
            return []
        filter = {}
        if prompt_ids:
            filter["_id"] = {"$in": [ObjectId(prompt.id) for prompt in prompts]}
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        await self.collection.delete_many(filter=filter)
        return prompts
