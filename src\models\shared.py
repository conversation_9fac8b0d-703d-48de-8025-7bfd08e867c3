from datetime import datetime, timezone
from enum import Enum
from typing import Generic, TypeVar

from humps import camelize
from pydantic import BaseModel


def to_camel(string):
    return camelize(string)


def to_iso_8601(dt: datetime) -> str:
    return dt.replace(tzinfo=timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")


class ConfigModel(BaseModel):
    class Config:
        alias_generator = to_camel
        populate_by_name = True
        json_encoders = {datetime: to_iso_8601}


class FileUpload(ConfigModel):
    content: bytes
    name: str
    content_type: str
    size: float


T = TypeVar("T")


class Pagination(ConfigModel):
    page_size: int = 10
    page_number: int = 1

    def skip(self):
        return (self.page_number - 1) * self.page_size

    def limit(self):
        return self.page_size


class PaginationResult(ConfigModel):
    page_size: int
    page_number: int
    total: int


class PaginationResponse(ConfigModel, Generic[T]):
    data: list[T]
    pagination: PaginationResult


class DeleteResponse(ConfigModel):
    count: int
    ids: list[str]


class SortOrder(str, Enum):
    ASC = "asc"
    DESC = "desc"


class DateCount(ConfigModel):
    date: str
    count: int
