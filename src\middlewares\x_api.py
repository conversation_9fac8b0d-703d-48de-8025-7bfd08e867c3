from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, Security
from fastapi.security import <PERSON><PERSON>eyHeader

from src.services.auth import AuthService

auth_service = AuthService()


async def validate_api_key(
    request: Request,
    api_key: str = Security(APIKeyHeader(name="X-API-Key")),
):
    if not api_key:
        raise HTTPException(status_code=401, detail="API key required")
    auth = await auth_service.authenticate_by_api_key(api_key)
    request.state.auth = auth
