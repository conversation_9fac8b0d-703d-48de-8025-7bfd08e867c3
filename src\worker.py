import os
from contextlib import asynccontextmanager

import sentry_sdk
from fastapi import Depends, FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from llama_index.core import Settings
from pydantic_core import PydanticSerializationError
from starlette.exceptions import HTTPException

from src.controllers.job_metadata import job_metadata_router
from src.middlewares.jwt_bearer import validate_token
from src.middlewares.request_log import RequestLogMiddleware
from src.rag.config import CHUNK_OVERLAP, CHUNK_SIZE, DEFAULT_EMBED_MODEL, DEFAULT_LLM
from src.util.logger import Logger
from src.util.scheduler import configure_scheduler, scheduler

Settings.llm = DEFAULT_LLM
Settings.embed_model = DEFAULT_EMBED_MODEL
Settings.chunk_size = CHUNK_SIZE
Settings.chunk_overlap = CHUNK_OVERLAP


def before_send(event, hint):
    if "exc_info" in hint:
        exc_type, exc_value, tb = hint["exc_info"]
        if isinstance(
            exc_value, PydanticSerializationError
        ) and "async_generator" in str(exc_value):
            return None
    return event


sentry_sdk.init(
    dsn=os.environ.get("SENTRY_DNS"),
    environment=os.environ.get("ENV"),
    attach_stacktrace=True,
    send_default_pii=True,
    traces_sample_rate=1.0,
    profiles_sample_rate=1.0,
    before_send=before_send,
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    configure_scheduler()
    if not scheduler.running:
        scheduler.start()
    logger = Logger("WorkerAPI")
    logger.info("Worker API started with scheduler")

    yield

    if scheduler.running:
        scheduler.shutdown()
    logger = Logger("WorkerAPI")
    logger.info("Worker API shutdown - scheduler stopped")


app = FastAPI(lifespan=lifespan)


@app.exception_handler(Exception)
async def global_exception_handler(_: Request, exception: Exception):
    logger = Logger("GlobalException")
    logger.exception(exception)

    if isinstance(exception, HTTPException):
        return JSONResponse(
            status_code=exception.status_code,
            content={"detail": exception.detail},
        )

    return JSONResponse(
        status_code=500,
        content={"detail": "An unexpected error occurred. Please try again later."},
    )


app.add_middleware(RequestLogMiddleware)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/", tags=["Index"])
async def index():
    return "Snapz Worker API"


app.include_router(
    job_metadata_router,
    tags=["Jobs"],
    prefix="/jobs",
    dependencies=[Depends(validate_token)],
)
