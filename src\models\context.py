from datetime import datetime

from bson import ObjectId
from pytz import utc

from src.models.shared import ConfigModel
from src.models.task import TaskStatus


class Context(ConfigModel):
    id: str
    project_id: str
    text: str
    conversation_id: str | None = None
    user_id: str | None = None
    external_id: str | None = None
    document_id: str | None = None
    message_id: str | None = None
    created_at: datetime = datetime.now(utc)


class ContextResponse(ConfigModel):
    id: str
    text: str
    conversation_id: str | None = None
    user_id: str | None = None
    external_id: str | None = None
    document_id: str | None = None
    message_id: str | None = None
    created_at: datetime = datetime.now(utc)
    status: TaskStatus | None = None


class PostContextBody(ConfigModel):
    text: str
    conversation_id: str | None = None
    external_id: str | None = None
    document_id: str | None = None
    message_id: str | None = None

class PostContextsBody(ConfigModel):
    contexts: list[PostContextBody]


class PatchContextBody(ConfigModel):
    text: str | None = None
    conversation_id: str | None = None
    user_id: str | None = None
    external_id: str | None = None
    document_id: str | None = None


class DeleteContextsBody(ConfigModel):
    context_ids: list[str] = []
    document_ids: list[str] = []
    external_ids: list[str] = []
    user_id: str | None = None
    conversation_id: str | None = None


class InsertContextInput(ConfigModel):
    project_id: str
    text: str
    conversation_id: str | None = None
    user_id: str | None = None
    external_id: str | None = None
    document_id: str | None = None
    message_id: str | None = None

    def to_dict(self) -> dict:
        conversation_id = (
            ObjectId(self.conversation_id) if self.conversation_id else None
        )
        document_id = ObjectId(self.document_id) if self.document_id else None
        return {
            "project_id": ObjectId(self.project_id),
            "text": self.text,
            "conversation_id": conversation_id or None,
            "document_id": document_id or None,
            "user_id": ObjectId(self.user_id) if self.user_id else None,
            "external_id": self.external_id or None,
            "message_id": ObjectId(self.message_id) if self.message_id else None,
            "created_at": datetime.now(utc),
        }


class UpdateContextInput(ConfigModel):
    text: str | None = None
    conversation_id: str | None = None
    user_id: str | None = None
    external_id: str | None = None
    document_id: str | None = None
    message_id: str | None = None