import asyncio
from datetime import datetime

from bson import ObjectId
from pymongo import DESCENDING, ReturnDocument
from pytz import utc

from src.models.job_metadata import JobMetadata, JobType, PeriodUnit
from src.models.shared import ConfigModel, Pagination, PaginationResponse, PaginationResult
from src.util.mongo import db


class InsertJobMetadataInput(ConfigModel):
    job_id: str
    project_id: str
    user_id: str
    name: str
    period_unit: PeriodUnit = PeriodUnit.NOW
    period_value: int = 1
    type: JobType | None = None
    metadata: dict = {}

    def to_dict(self) -> dict:
        return {
            "job_id": self.job_id,
            "project_id": self.project_id,
            "user_id": self.user_id,
            "name": self.name,
            "period_unit": self.period_unit,
            "period_value": self.period_value,
            "type": self.type,
            "metadata": self.metadata,
            "created_at": datetime.now(utc),
        }


class JobMetadataRepository:
    def __init__(self):
        self.collection = db.get_collection("job_metadata")

    def _to_model(self, job_metadata_dict: dict) -> JobMetadata:
        return JobMetadata(
            id=str(job_metadata_dict.get("_id")),
            job_id=str(job_metadata_dict.get("job_id")),
            project_id=str(job_metadata_dict.get("project_id")),
            user_id=str(job_metadata_dict.get("user_id")),
            name=str(job_metadata_dict.get("name")),
            period_unit=job_metadata_dict.get("period_unit"),
            period_value=job_metadata_dict.get("period_value"),
            type=job_metadata_dict.get("type"),
            metadata=job_metadata_dict.get("metadata"),
            created_at=job_metadata_dict.get("created_at"),
        )

    async def find_one(
        self,
        job_metadata_id: str | None = None,
        job_id: str | None = None,
        project_id: str | None = None,
        user_id: str | None = None,
    ) -> JobMetadata | None:
        filter = {}
        if job_metadata_id:
            filter["_id"] = ObjectId(job_metadata_id)
        if job_id:
            filter["job_id"] = job_id
        if project_id:
            filter["project_id"] = project_id
        if user_id:
            filter["user_id"] = user_id

        job_metadata_dict = await self.collection.find_one(
            filter=filter,
            sort=[("created_at", DESCENDING)],
        )
        return self._to_model(job_metadata_dict) if job_metadata_dict else None

    async def find_many(
        self,
        job_metadata_ids: list[str] = [],
        job_ids: list[str] = [],
        project_id: str | None = None,
        user_id: str | None = None,
        type: JobType | None = None,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[JobMetadata]:
        filter = {}
        if job_metadata_ids:
            filter["_id"] = {"$in": [ObjectId(id) for id in job_metadata_ids]}
        if job_ids:
            filter["job_id"] = {"$in": job_ids}
        if project_id:
            filter["project_id"] = project_id
        if user_id:
            filter["user_id"] = user_id
        if type:
            filter["type"] = type

        job_metadata_dicts = await self.collection.find(
            filter=filter, sort=sort
        ).to_list()
        return [self._to_model(job_metadata) for job_metadata in job_metadata_dicts]

    async def paginate(
        self,
        project_id: str | None = None,
        user_id: str | None = None,
        type: JobType | None = None,
        name: str | None = None,
        page_size: int = 10,
        page_number: int = 1,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> PaginationResponse[JobMetadata]:
        filter = {}
        if project_id:
            filter["project_id"] = project_id
        if user_id:
            filter["user_id"] = user_id
        if type:
            filter["type"] = type
        if name:
            filter["name"] = {"$regex": name, "$options": "i"}

        pagination = Pagination(page_size=page_size, page_number=page_number)

        (total, job_metadata_dicts) = await asyncio.gather(
            self.collection.count_documents(filter=filter),
            self.collection.find(
                filter=filter,
                limit=pagination.limit(),
                skip=pagination.skip(),
                sort=sort,
            ).to_list(),
        )

        return PaginationResponse(
            data=[self._to_model(job_metadata_dict) for job_metadata_dict in job_metadata_dicts],
            pagination=PaginationResult(
                total=total,
                page_size=page_size,
                page_number=page_number,
            ),
        )

    async def insert_one(self, input: InsertJobMetadataInput) -> JobMetadata | None:
        result = await self.collection.insert_one(input.to_dict())
        return await self.find_one(job_metadata_id=str(result.inserted_id))

    async def insert_many(
        self,
        inputs: list[InsertJobMetadataInput],
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[JobMetadata]:
        job_metadata_dicts = [input.to_dict() for input in inputs]
        result = await self.collection.insert_many(job_metadata_dicts)
        return await self.find_many(
            job_metadata_ids=[str(id) for id in result.inserted_ids], sort=sort
        )

    async def update_one(
        self,
        job_metadata_id: str,
        job_id: str | None = None,
        name: str | None = None,
        period_unit: PeriodUnit | None = None,
        period_value: int | None = None,
        metadata: dict | None = None,
    ) -> JobMetadata | None:
        update = {}
        if job_id:
            update["job_id"] = job_id
        if name:
            update["name"] = name
        if period_unit:
            update["period_unit"] = period_unit
        if period_value:
            update["period_value"] = period_value
        if metadata:
            update["metadata"] = metadata

        job_metadata_dict = await self.collection.find_one_and_update(
            filter={"_id": ObjectId(job_metadata_id)},
            update={"$set": update},
            return_document=ReturnDocument.AFTER,
        )
        return self._to_model(job_metadata_dict) if job_metadata_dict else None

    async def delete_one(
        self,
        job_metadata_id: str,
        project_id: str | None = None,
        user_id: str | None = None,
    ) -> JobMetadata | None:
        filter = {}
        if job_metadata_id:
            filter["_id"] = ObjectId(job_metadata_id)
        if project_id:
            filter["project_id"] = project_id
        if user_id:
            filter["user_id"] = user_id

        job_metadata = await self.find_one(job_metadata_id=job_metadata_id)
        if not job_metadata:
            return None

        await self.collection.delete_one(filter=filter)
        return job_metadata

    async def delete_many(
        self,
        job_metadata_ids: list[str] = [],
        project_id: str | None = None,
        user_id: str | None = None,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[JobMetadata]:
        filter = {}
        if job_metadata_ids:
            filter["_id"] = {"$in": [ObjectId(id) for id in job_metadata_ids]}
        if project_id:
            filter["project_id"] = project_id
        if user_id:
            filter["user_id"] = user_id

        if not filter:
            return []

        job_metadatas = await self.find_many(
            job_metadata_ids=job_metadata_ids,
            project_id=project_id,
            user_id=user_id,
            sort=sort,
        )

        await self.collection.delete_many(filter=filter)
        return job_metadatas
