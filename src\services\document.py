import asyncio
import uuid

import aiohttp
from fastapi import HTTPException, UploadFile
from pymongo import ASCENDING, DESCENDING

from src.models.auth import Auth
from src.models.context import DeleteContextsBody
from src.models.document import (
    ContentType,
    DeleteDocumentsBody,
    Document,
    DocumentResponse,
    DocumentSortField,
    DocumentUsageResponse,
    FileUpload,
    InsertDocumentInput,
    PostDocumentsBody,
)
from src.models.shared import DeleteResponse, Pagination, PaginationResponse, SortOrder
from src.models.task import InsertTaskInput, TaskPriority, TaskStatus
from src.repositories.conversation import ConversationRepository
from src.repositories.document import DocumentRepository
from src.repositories.task import TaskRepository
from src.services.context import ContextService
from src.services.pinecone import PineconeService
from src.services.spaces import SpacesService
from src.util.logger import Logger
from src.util.text import slugify_file_name
from src.util.logger import Logger


class DocumentService:
    def __init__(self):
        self.spaces_service = SpacesService()
        self.pinecone_service = PineconeService()
        self.task_repository = TaskRepository()
        self.document_respository = DocumentRepository()
        self.context_service = ContextService()
        self.conversation_repository = ConversationRepository()
        self.logger = Logger("DocumentService")

    async def create_document_from_file(
        self,
        auth: Auth,
        file_upload: FileUpload,
        conversation_id: str | None = None,
        user_id: str | None = None,
        external_id: str | None = None,
        source_url: str | None = None,
        metadata: str | None = None,
    ) -> Document:
        try:
            existing_document = await self.document_respository.find_one(
                slug=file_upload.slug,
                project_id=auth.project.id,
            )

            if existing_document:
                return existing_document

            was_file_uploaded = await self.spaces_service.upload_file(
                content=file_upload.content,
                path=file_upload.path,
                content_type=file_upload.content_type,
            )
            if not was_file_uploaded:
                raise HTTPException(status_code=500, detail="Error uploading file")

            url = self.spaces_service.get_file_url(file_upload.path)
            document = await self.document_respository.insert_one(
                input=InsertDocumentInput(
                    project_id=auth.project.id,
                    name=file_upload.name,
                    slug=file_upload.slug,
                    url=url,
                    path=file_upload.path,
                    content_type=file_upload.content_type,
                    size=file_upload.size,
                    source_url=source_url,
                    conversation_id=conversation_id,
                    user_id=user_id,
                    external_id=external_id,
                    metadata=metadata,
                )
            )

            if not document:
                raise HTTPException(status_code=500, detail="Error creating document")

            asyncio.create_task(
                self.task_repository.insert_one(
                    input=InsertTaskInput(
                        document_id=document.id,
                        priority=(
                            TaskPriority.LOW
                            if auth.is_integration()
                            else TaskPriority.HIGH
                        ),
                    )
                ),
            )

            if metadata:
                asyncio.create_task(
                    self.context_service.create(
                        auth=auth,
                        text=metadata,
                        conversation_id=conversation_id,
                        user_id=user_id,
                        external_id=external_id,
                        document_id=document.id,
                    )
                )

            return document
        except Exception as e:
            self.logger.error(
                f"Error creating document from file: {file_upload.name}", e
            )
            return None

    async def create_document_from_url(
        self,
        auth: Auth,
        url: str,
        conversation_id: str | None = None,
        external_id: str | None = None,
        name: str | None = None,
        content_type: str | None = None,
        source_url: str | None = None,
        metadata: str | None = None,
    ) -> Document | None:
        try:
            file_upload = await self._download_file(
                url=url,
                organization_id=auth.organization.id,
                project_id=auth.project.id,
                content_type=content_type,
                name=name,
            )

            was_file_uploaded = await self.spaces_service.upload_file(
                content=file_upload.content,
                path=file_upload.path,
                content_type=file_upload.content_type,
            )
            if not was_file_uploaded:
                raise HTTPException(status_code=500, detail="Error uploading file")

            url = self.spaces_service.get_file_url(file_upload.path)
            document = await self.document_respository.insert_one(
                input=InsertDocumentInput(
                    project_id=auth.project.id,
                    name=file_upload.name,
                    slug=file_upload.slug,
                    url=url,
                    path=file_upload.path,
                    content_type=file_upload.content_type,
                    size=file_upload.size,
                    source_url=source_url,
                    conversation_id=conversation_id,
                    external_id=external_id,
                    metadata=metadata,
                )
            )

            if not document:
                raise HTTPException(status_code=500, detail="Error creating document")

            asyncio.create_task(
                self.task_repository.insert_one(
                    input=InsertTaskInput(
                        document_id=document.id,
                        priority=(
                            TaskPriority.LOW
                            if auth.is_integration()
                            else TaskPriority.HIGH
                        ),
                    )
                ),
            )

            if metadata:
                asyncio.create_task(
                    self.context_service.create(
                        auth=auth,
                        text=metadata,
                        conversation_id=conversation_id,
                        external_id=external_id,
                        document_id=document.id,
                    )
                )

            return document
        except Exception as e:
            self.logger.error(f"Error creating document from url: {url}", e)
            return None

    async def post_files(
        self,
        auth: Auth,
        files: list[UploadFile],
        conversation_id: str | None = None,
        external_id: str | None = None,
        source_url: str | None = None,
        metadata: str | None = None,
    ) -> list[DocumentResponse]:
        if not files:
            raise HTTPException(status_code=400, detail="No files uploaded")

        file_uploads = await asyncio.gather(
            *[
                self._to_file_upload(
                    file=file,
                    organization_id=auth.organization.id,
                    project_id=auth.project.id,
                )
                for file in files
            ]
        )

        documents = await asyncio.gather(
            *[
                self.create_document_from_file(
                    auth=auth,
                    file_upload=file_upload,
                    conversation_id=conversation_id,
                    user_id=auth.user.id if auth.user else None,
                    external_id=external_id,
                    source_url=source_url,
                    metadata=metadata,
                )
                for file_upload in file_uploads
            ]
        )

        documents = [document for document in documents if document]

        if not documents:
            raise HTTPException(
                status_code=500, detail="Error creating documents by file"
            )

        return [
            self._to_document_response(document, TaskStatus.PENDING)
            for document in documents
        ]

    async def post_urls(
        self,
        auth: Auth,
        body: PostDocumentsBody,
    ) -> list[DocumentResponse]:
        if not body.documents:
            raise HTTPException(status_code=400, detail="No urls provided")

        documents = await asyncio.gather(
            *[
                self.create_document_from_url(
                    auth=auth,
                    url=input.url,
                    name=input.name,
                    content_type=input.content_type,
                    source_url=input.source_url,
                    conversation_id=input.conversation_id,
                    external_id=input.external_id,
                    metadata=input.metadata,
                )
                for input in body.documents
            ]
        )

        documents = [document for document in documents if document]

        if not documents:
            raise HTTPException(
                status_code=500, detail="Error creating documents by url"
            )

        return [
            self._to_document_response(document, TaskStatus.PENDING)
            for document in documents
        ]

    async def get_one(
        self,
        auth: Auth,
        document_id: str,
    ) -> DocumentResponse:
        (document, task) = await asyncio.gather(
            self.document_respository.find_one(
                document_id=document_id,
                project_id=auth.project.id,
            ),
            self.task_repository.find_last_one(
                document_id=document_id,
            ),
        )
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        return self._to_document_response(document, task.status)

    async def get_many(
        self,
        auth: Auth,
        document_ids: list[str] | None = None,
        external_ids: list[str] | None = None,
        user_id: str | None = None,
        conversation_id: str | None = None,
    ) -> list[DocumentResponse]:
        documents = await self.document_respository.find_many(
            project_id=auth.project.id,
            document_ids=document_ids,
            external_ids=external_ids,
            user_id=user_id,
            conversation_id=conversation_id,
        )

        if not documents:
            return []

        tasks = await self.task_repository.find_many(
            document_ids=[document.id for document in documents],
            sort=[("created_at", ASCENDING)],
        )
        task_statuses = {task.document_id: task.status for task in tasks}

        return [
            self._to_document_response(document, task_statuses.get(document.id))
            for document in documents
        ]

    async def paginate(
        self,
        auth: Auth,
        document_ids: list[str] = [],
        external_ids: list[str] = [],
        name: str | None = None,
        conversation_id: str | None = None,
        user_id: str | None = None,
        size: float | None = None,
        content_type: ContentType | None = None,
        start_date: str | None = None,
        end_date: str | None = None,
        page_size: int = 10,
        page_number: int = 1,
        sort_by: DocumentSortField | None = None,
        sort_order: SortOrder | None = None,
    ) -> PaginationResponse[DocumentResponse]:
        sort = [("created_at", DESCENDING)]

        if sort_by:
            match sort_by:
                case DocumentSortField.SIZE:
                    sort = [
                        (
                            sort_by,
                            ASCENDING if sort_order == SortOrder.ASC else DESCENDING,
                        )
                    ]
                case DocumentSortField.CREATED_AT:
                    sort = [
                        (
                            sort_by,
                            ASCENDING if sort_order == SortOrder.ASC else DESCENDING,
                        )
                    ]

        result = await self.document_respository.paginate(
            project_id=auth.project.id,
            document_ids=document_ids,
            external_ids=external_ids,
            name=name,
            conversation_id=conversation_id,
            user_id=auth.get_member_user_id(user_id),
            size=size,
            content_type=content_type,
            start_date=start_date,
            end_date=end_date,
            page_size=page_size,
            page_number=page_number,
            sort=sort,
        )

        if not result:
            return PaginationResponse(data=[], pagination=Pagination())

        tasks = await self.task_repository.find_many(
            document_ids=[document.id for document in result.data],
            sort=[("created_at", ASCENDING)],
        )
        task_statuses = {task.document_id: task.status for task in tasks}

        document_responses = [
            self._to_document_response(document, task_statuses.get(document.id))
            for document in result.data
        ]
        return PaginationResponse(data=document_responses, pagination=result.pagination)

    async def usage(
        self,
        auth: Auth,
        user_id: str | None = None,
    ) -> DocumentUsageResponse:
        return await self.document_respository.usage(
            project_id=auth.project.id,
            user_id=auth.get_member_user_id(user_id),
        )

    async def delete_many(
        self,
        auth: Auth,
        body: DeleteDocumentsBody,
    ) -> DeleteResponse:
        if not auth.has_permission(body.user_id):
            raise HTTPException(
                status_code=403,
                detail="You can only delete your documents",
            )
        documents = await self.document_respository.delete_many(
            project_id=auth.project.id,
            document_ids=body.document_ids,
            external_ids=body.external_ids,
            user_id=body.user_id,
            conversation_id=body.conversation_id,
        )

        if not documents:
            return DeleteResponse(count=0, ids=[])

        document_ids = [document.id for document in documents]

        asyncio.gather(
            *[
                self.context_service.delete_many(
                    auth=auth,
                    body=DeleteContextsBody(document_ids=document_ids),
                ),
                self.pinecone_service.delete(
                    organization_id=auth.organization.id,
                    project_id=auth.project.id,
                    document_ids=document_ids,
                ),
                self.task_repository.delete_many(
                    document_ids=document_ids,
                ),
                self.conversation_repository.delete_document_scores(
                    document_ids=document_ids,
                ),
                *[
                    self.spaces_service.delete_file(
                        path=document.path,
                    )
                    for document in documents
                ],
            ]
        )

        return DeleteResponse(
            count=len(documents),
            ids=[document.id for document in documents],
        )

    async def _to_file_upload(
        self,
        file: UploadFile,
        organization_id: str,
        project_id: str,
    ) -> FileUpload:
        await file.seek(0)
        slug = slugify_file_name(file.filename)
        return FileUpload(
            content=file.file.read(),
            name=file.filename,
            slug=slug,
            size=file.size,
            path=f"files/{organization_id}/{project_id}/{slug}",
            content_type=file.content_type,
        )

    async def _download_file(
        self,
        url: str,
        organization_id: str,
        project_id: str,
        content_type: str | None = None,
        name: str | None = None,
    ) -> FileUpload | None:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    response.raise_for_status()
                    content = await response.read()
                    content_type = content_type or response.headers.get("content-type")
                    size = int(response.headers.get("content-length", 0))
                    if not content or size == 0:
                        raise HTTPException(
                            status_code=500, detail="No content in file"
                        )
                    if not name:
                        content_disposition = response.headers.get(
                            "content-disposition"
                        )
                        if content_disposition:
                            name = content_disposition.split("filename=")[1].strip('"')
                        else:
                            name = uuid.uuid4().hex
                    slug = slugify_file_name(name)
                    return FileUpload(
                        content=content,
                        name=name,
                        slug=slug,
                        path=f"files/{organization_id}/{project_id}/{slug}",
                        content_type=content_type,
                        size=size,
                    )
        except Exception as e:
            self.logger.error(f"Error downloading file from url: {url}", e)
            return None

    def _to_document_response(
        self, document: Document, status: TaskStatus | None = None
    ) -> DocumentResponse:
        return DocumentResponse(
            id=document.id,
            name=document.name,
            slug=document.slug,
            url=document.url,
            content_type=document.content_type,
            size=document.size,
            source_url=document.source_url,
            conversation_id=document.conversation_id,
            user_id=document.user_id,
            external_id=document.external_id,
            created_at=document.created_at,
            status=status,
        )
