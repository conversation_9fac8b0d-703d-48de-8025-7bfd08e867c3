from datetime import datetime
from enum import Enum

from bson import ObjectId
from llama_index.core.schema import TextNode
from pydantic import BaseModel, Field
from pytz import utc

from src.models.shared import ConfigModel
from src.models.task import Task, TaskPriority, TaskStatus


class FileUpload(ConfigModel):
    content: bytes
    name: str
    slug: str
    size: float
    path: str
    content_type: str | None = None


class DocumentPage(ConfigModel):
    text: str
    page: int


class ContentType(str, Enum):
    PDF = "application/pdf"
    CSV = "text/csv"
    DOC = "application/msword"
    DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    TXT = "text/plain"
    RTF = "application/rtf"
    PPT = "application/vnd.ms-powerpoint"
    PPTX = "application/vnd.openxmlformats-officedocument.presentationml.presentation"
    XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    XLS = "application/vnd.ms-excel"
    JPG = "image/jpeg"
    JPEG = "image/jpeg"
    PNG = "image/png"
    HTML = "text/html"
    NUMBERS = "application/vnd.apple.numbers"
    PAGES = "application/vnd.apple.pages"
    KEY = "application/vnd.apple.keynote"


CONTENT_TYPES = [content_type.value for content_type in ContentType]


class DocumentType(Enum):
    ABANDONMENT = "abandonment"
    ADDENDUM = "addendum"
    ADVERTISING_CAMPAIGN = "advertising_campaign"
    AMENDMENT = "amendment"
    ANNUAL_REPORT = "annual_report"
    APPRAISAL = "appraisal"
    ARCHITECTS_SUPPLEMENTAL_INSTRUCTION = "asi_architects_supplemental_instruction"
    ARCHITECTURAL_RENDERING = "architectural_rendering"
    AS_BUILT_DRAWING = "as_built_drawing"
    ASSET_MANAGEMENT_REPORT = "asset_management_report"
    ASSET_PURCHASE_AGREEMENT = "asset_purchase_agreement"
    BALANCE_SHEET = "balance_sheet"
    BID_EVALUATION = "bid_evaluation"
    BID_PROPOSAL = "bid_proposal"
    BILL_OF_QUANTITIES = "bill_of_quantities"
    BILL_OF_SALE = "bill_of_sale"
    BLUEPRINT_DRAWING = "blueprint_drawing"
    BODY_OF_KNOWLEDGE = "body_of_knowledge"
    BROCHURE = "brochure"
    BUDGET_OVERVIEW = "budget_overview"
    BUDGET_REPORT = "budget_report"
    BUILDING_SECTIONS = "building_sections"
    BUSINESS_CASE = "business_case"
    BUSINESS_REQUIREMENTS_DOCUMENT = "brd"
    CAPITAL_STRUCTURE_ANALYSIS = "capital_structure_analysis"
    CASH_FLOW_PROJECTION = "cash_flow_projection"
    CASH_FLOW_STATEMENT = "cash_flow_statement"
    CERTIFICATE_FOR_PAYMENT = "cfp_certificate_for_payment"
    CERTIFICATE_OF_OCCUPANCY = "certificate_of_occupancy"
    CERTIFICATE_OF_SUBSTANTIAL_COMPLETION = "certificate_of_substantial_completion"
    CHANGE_DIRECTIVE = "change_directive"
    CHANGE_ORDER = "change_order"
    CHANGE_PROPOSAL = "change_proposal"
    CHANGE_REQUEST = "change_request"
    CIVIL_DRAWINGS = "civil_drawings"
    CLAIMS = "claims"
    CLOSEOUT = "close_out"
    CLOSING_DOCUMENTS = "closing_documents"
    COMMISSIONING_REPORT = "commissioning_report"
    COMMUNITY_DEVELOPMENT_PLAN = "community_development_plan"
    COMPETITIVE_ANALYSIS = "competitive_analysis"
    CONSTRUCTION_LOG = "construction_log"
    CONSTRUCTION_MANUAL = "construction_manual"
    CONSTRUCTION_PERMIT = "construction_permit"
    CONTRACT = "contract"
    CORPORATE_DOCUMENTS = "corporate_documents"
    COST_ESTIMATE = "cost_estimate"
    DAILY_REPORT = "daily_report"
    DEAL_SUMMARY = "deal_summary"
    DESIGN_PATENT = "design_patent"
    DETAIL_DRAWINGS = "detail_drawings"
    DEVELOPMENT_FEASIBILITY_REPORT = "development_feasibility_report"
    DEVELOPMENT_PROPOSAL = "development_proposal"
    DISCLOSURE_SCHEDULE = "disclosure_schedule"
    DUE_DILIGENCE = "due_diligence"
    DUE_DILIGENCE_REPORT = "due_diligence_report"
    EARNINGS_CALL_TRANSCRIPT = "earnings_call_transcript"
    ELEVATIONS = "elevations"
    ENGAGEMENT_LETTER = "engagement_letter"
    ENGINEERING_REPORT = "engineering_report"
    ENVIRONMENTAL_IMPACT_REPORT = "environmental_impact_report"
    ENVIRONMENTAL_IMPACT_STATEMENT = "environmental_impact_statement"
    ENVIRONMENTAL_SUSTAINABILITY_PLAN = "environmental_sustainability_plan"
    EQUIPMENT_MANUAL = "equipment_manual"
    ESCROW_AGREEMENT = "escrow_agreement"
    ESTIMATE = "estimate"
    EXCLUSIVITY_AGREEMENT = "exclusivity_agreement"
    EXIT_STRATEGY_PLAN = "exit_strategy_plan"
    EXPENSE = "expense"
    EXTENSION_OF_TIME_REQUEST = "extension_of_time_request"
    FABRICATION_DRAWINGS = "fabrication_drawings"
    FIELD_ORDER = "field_order"
    FIELD_REPORT = "field_report"
    FINAL_INSPECTION_CERTIFICATE = "final_inspection_certificate"
    FINANCIAL_MODEL = "financial_model"
    FINANCIAL_STATEMENT = "financial_statement"
    FOUNDATION_PLAN = "foundation_plan"
    FRANCHISE_AGREEMENT = "franchise_agreement"
    FRANCHISE_OPERATIONS_MANUAL = "franchise_operations_manual"
    GEOTECHNICAL_REPORT = "geotechnical_report"
    GRANTED_PATENT = "granted_patent"
    HAZMAT_REPORT = "hazmat_report"
    HOUSING_NEEDS_ASSESSMENT = "housing_needs_assessment"
    INCOME_STATEMENT = "income_statement"
    INFRASTRUCTURE_ASSESSMENT = "infrastructure_assessment"
    INSPECTION_REPORT = "inspection_report"
    INSURANCE_CERTIFICATE = "insurance_certificate"
    INVESTMENT_MEMO = "investment_memo"
    INVESTOR_PRESENTATION = "investor_presentation"
    INVOICE_RECEIPT = "invoice_receipt"
    JOB_COMPLETION_CERTIFICATE = "job_completion_certificate"
    JOINT_VENTURE_AGREEMENT = "joint_venture_agreement"
    LABOR_LOG = "labor_log"
    LAND_USE_PLAN = "land_use_plan"
    LEASE_AGREEMENT = "lease_agreement"
    LEASING_PACKAGE = "leasing_package"
    LEGAL_BRIEF = "legal_brief"
    LEGAL_OPINION = "legal_opinion"
    LETTER_OF_CREDIT = "letter_of_credit"
    LETTER_OF_INTENT = "loi"
    LIEN_WAIVER = "lien_waiver"
    LOAN_PACKAGE = "loan_package"
    MAINTENANCE_CHECKLIST = "maintenance_checklist"
    MAINTENANCE_LOG = "maintenance_log"
    MARKET_ANALYSIS = "market_analysis"
    MARKET_ANALYSIS_REPORT = "market_analysis_report"
    MARKETING_PLAN = "marketing_plan"
    MARKETING_TEMPLATE = "marketing_template"
    MASTER_PLAN = "master_plan"
    MATERIAL_LOG = "material_log"
    MATERIAL_TAKEOFF = "material_takeoff"
    MECHANICS_LIEN = "mechanics_lien"
    MECHANICS_LIEN_RELEASE = "mechanics_lien_release"
    MEETING_MINUTES = "meeting_minutes"
    MEMORANDUM_OF_UNDERSTANDING = "mou"
    MEP_DRAWINGS = "mep_drawings"
    MOLD_INSPECTION_REPORT = "mold_inspection_report"
    MORTGAGE_AGREEMENT = "mortgage_agreement"
    NON_DISCLOSURE_AGREEMENT = "nda"
    NOTICE_OF_COMPLETION = "notice_of_completion"
    NOTICE_OF_DEFAULT = "notice_of_default"
    NOTICE_TO_OWNER = "notice_to_owner"
    NOTICE_TO_PROCEED = "notice_to_proceed"
    OFFERING_MEMORANDUM = "offering_memorandum"
    OPEN_SPACE_PLAN = "open_space_plan"
    OPERATING_STATEMENT = "operating_statement"
    PARTNERSHIP_AGREEMENT = "partnership_agreement"
    PARTS_CATALOG = "parts_catalog"
    PATENT_APPLICATION = "patent_application"
    PATENT_EXAMINER = "patent_examiner"
    PATENT_INFRINGEMENT = "patent_infringement"
    PATENT_TERM = "patent_term"
    PAY_APPLICATION = "pay_application"
    PAYMENT_BOND = "payment_bond"
    PERFORMANCE_BOND = "performance_bond"
    PERMIT_APPLICATION = "permit_application"
    PERSONAL_COACHING_AGREEMENT = "personal_coaching_agreement"
    PERSONAL_COACHING_PLAN = "personal_coaching_plan"
    POWER_OF_ATTORNEY = "power_of_attorney"
    PRELIMINARY_NOTICE = "preliminary_notice"
    PREVENTIVE_MAINTENANCE = "preventive_maintenance"
    PRESS_RELEASE = "press_release"
    PRIOR_ART = "prior_art"
    PRODUCT_REQUIREMENTS_DOCUMENT = "prd"
    PRODUCT_ROADMAP = "roadmap"
    PROFORMA_INVOICE = "proforma_invoice"
    PROPERTY_APPRAISAL = "property_appraisal"
    PROPERTY_CONDITION_REPORT = "property_condition_report"
    PROPERTY_LISTING = "property_listing"
    PROPOSED_CHANGE_ORDER = "pco_proposed_change_order"
    PUBLIC_PARTICIPATION_REPORT = "public_participation_report"
    PUNCH_LIST = "punch_list"
    PURCHASE_ORDER = "purchase_order"
    QUALITY_ASSURANCE_PLAN = "quality_assurance_plan"
    QUALITY_CONTROL_PLAN = "quality_control_plan"
    QUARTERLY_REPORT = "quarterly_report"
    QUOTE_PROPOSAL = "quote_proposal"
    RELEASE_OF_LIABILITY = "release_of_liability"
    RENT_CONTRACT = "rent_contract"
    RENT_ROLL = "rent_roll"
    REQUEST_FOR_CLARIFICATION = "rfc_request_for_clarification"
    REQUEST_FOR_INFORMATION = "rfi_request_for_information"
    RESEARCH_PAPER = "research_paper"
    RESUME = "resume"
    RISK_ASSESSMENT = "risk_assessment"
    RISK_MANAGEMENT_REPORT = "risk_management_report"
    ROOF_PLAN = "roof_plan"
    SAFETY_CHECKLIST = "safety_checklist"
    SAFETY_PLAN = "safety_plan"
    SAFETY_PROCEDURE = "safety_procedure"
    SALES_TEASER = "sales_teaser"
    SCHEDULE_GANTT_CHART = "schedule_gantt_chart"
    SCOPE_OF_WORK = "scope_of_work"
    SENSITIVITY_ANALYSIS = "sensitivity_analysis"
    SERVICE_BULLETIN = "service_bulletin"
    SETTLEMENT_STATEMENT = "settlement_statement"
    SHARE_PURCHASE_AGREEMENT = "share_purchase_agreement"
    SHAREHOLDER_AGREEMENT = "shareholder_agreement"
    SHOP_DRAWINGS = "shop_drawings"
    SITE_ANALYSIS_REPORT = "site_analysis_report"
    SITE_PLAN = "site_plan"
    SMART_CITIES_REPORT = "smart_cities_report"
    SOIL_REPORT = "soil_report"
    SPARE_PARTS = "spare_parts"
    SPECIFICATION = "specification"
    STORMWATER_POLLUTION_PREVENTION_PLAN = "stormwater_pollution_prevention_plan"
    STRATEGIC_PLANNING_DOCUMENT = "strategic_planning_document"
    STRUCTURAL_DRAWINGS = "structural_drawings"
    SUBCONTRACTOR_AGREEMENT = "subcontractor_agreement"
    SUBMITTAL = "submittal"
    TEASER = "teaser"
    TENDER_DOCUMENT = "tender_document"
    TERM_SHEET = "term_sheet"
    TITLE_DEED = "title_deed"
    TRAFFIC_IMPACT_STUDY = "traffic_impact_study"
    TRANSPORTATION_MASTER_PLAN = "transportation_master_plan"
    TROUBLESHOOTING_GUIDE = "troubleshooting_guide"
    UNDERWRITING_MEMO = "underwriting_memo"
    URBAN_DESIGN_GUIDELINES = "urban_design_guidelines"
    URBAN_INFRASTRUCTURE_PLAN = "urban_infrastructure_plan"
    URBAN_RENEWAL_PROPOSAL = "urban_renewal_proposal"
    USER_PERSONA = "persona"
    USER_STORY = "user_story"
    UTILITY_BILL = "utility_bill"
    UTILITY_PATENT = "utility_patent"
    VARIATION_ORDER = "variation_order"
    WARRANTY = "warranty"
    WATER_TEST_REPORT = "water_test_report"
    WORK_ORDER = "work_order"
    ZONING_MAP = "zoning_map"
    ZONING_PERMIT = "zoning_permit"


DOCUMENT_TYPES = [document_type.value for document_type in DocumentType]


class DocumentCategory(Enum):
    ACCOUNTING = "accounting"
    ADVERTISING = "advertising"
    AEROSPACE = "aerospace"
    AGRICULTURE = "agriculture"
    APPAREL = "apparel"
    ARCHITECTURE = "architecture"
    ARTS_ENTERTAINMENT = "arts_entertainment"
    AUTOMOTIVE = "automotive"
    AVIATION = "aviation"
    BANKING = "banking"
    BIOTECHNOLOGY = "biotechnology"
    BROADCASTING = "broadcasting"
    BUILDING_MATERIALS = "building_materials"
    BUSINESS_CONSULTING = "business_consulting"
    CHEMICALS = "chemicals"
    CIVIL_ENGINEERING = "civil_engineering"
    COMMERCIAL_REAL_ESTATE = "commercial_real_estate"
    COMPUTER_GAMES = "computer_games"
    COMPUTER_HARDWARE = "computer_hardware"
    COMPUTER_NETWORKING = "computer_networking"
    COMPUTER_SOFTWARE = "computer_software"
    COMPUTER_NETWORK_SECURITY = "computer_network_security"
    CONSTRUCTION = "construction"
    CONSUMER_ELECTRONICS = "consumer_electronics"
    CONSUMER_GOODS = "consumer_goods"
    COSMETICS = "cosmetics"
    DEFENSE_SPACE = "defense_space"
    DESIGN = "design"
    E_COMMERCE = "e_commerce"
    EDUCATION = "education"
    ELECTRICAL_ENGINEERING = "electrical_engineering"
    ELECTRONICS = "electronics"
    ENERGY = "energy"
    ENGINEERING = "engineering"
    ENVIRONMENTAL_SERVICES = "environmental_services"
    EVENT_PLANNING = "event_planning"
    FACILITIES_SERVICES = "facilities_services"
    FRANCHISE = "franchise"
    E_LEARNING = "e_learning"
    FINANCIAL_SERVICES = "financial_services"
    FOOD_BEVERAGE = "food_beverage"
    GOVERNMENT_ADMINISTRATION = "government_administration"
    GRAPHIC_DESIGN = "graphic_design"
    HARDWARE = "hardware"
    HEALTHCARE = "healthcare"
    HEAVY_EQUIPMENT = "heavy_equipment"
    HIGHER_EDUCATION = "higher_education"
    HOSPITALITY = "hospitality"
    HUMAN_RESOURCES = "human_resources"
    IMPORT_EXPORT = "import_export"
    INDUSTRIAL_AUTOMATION = "industrial_automation"
    INDUSTRIAL_MANUFACTURING = "industrial_manufacturing"
    INFORMATION_TECHNOLOGY = "information_technology"
    INSURANCE = "insurance"
    INTERIOR_DESIGN = "interior_design"
    INTERNATIONAL_TRADE_DEVELOPMENT = "international_trade_development"
    INTERNET = "internet"
    INVESTMENT_BANKING = "investment_banking"
    JEWELRY = "jewelry"
    JOURNALISM = "journalism"
    LANDSCAPING = "landscaping"
    LEGAL = "legal"
    LOGISTICS = "logistics"
    LUXURY_GOODS = "luxury_goods"
    MANAGEMENT_CONSULTING = "management_consulting"
    MANUFACTURING = "manufacturing"
    MARINE = "marine"
    MARKET_RESEARCH = "market_research"
    MARKETING_ADVERTISING = "marketing_advertising"
    MECHANICAL_ENGINEERING = "mechanical_engineering"
    MEDIA_PRODUCTION = "media_production"
    MEDICAL_DEVICES = "medical_devices"
    MINING_METALS = "mining_metals"
    MUSIC = "music"
    NANOTECHNOLOGY = "nanotechnology"
    NON_PROFIT = "non_profit"
    NURSING = "nursing"
    OIL_GAS = "oil_gas"
    ONLINE_MEDIA = "online_media"
    PACKAGING = "packaging"
    PHARMACEUTICALS = "pharmaceuticals"
    PHOTOGRAPHY = "photography"
    PUBLIC_ADMINISTRATION = "public_administration"
    PUBLIC_RELATIONS = "public_relations"
    PUBLISHING = "publishing"
    REAL_ESTATE = "real_estate"
    RENEWABLE_ENERGY = "renewable_energy"
    RETAIL = "retail"
    ROBOTICS = "robotics"
    SAAS = "saas"
    SECURITY = "security"
    SEMICONDUCTOR = "semiconductor"
    SERVICES = "services"
    SHIPPING = "shipping"
    SOCIAL_MEDIA = "social_media"
    SOFTWARE = "software"
    SPORTS = "sports"
    STAFFING_RECRUITING = "staffing_recruiting"
    SUPPLY_CHAIN = "supply_chain"
    TELECOMMUNICATIONS = "telecommunications"
    TEXTILE = "textile"
    TOURISM_TRAVEL = "tourism_travel"
    TRANSPORTATION = "transportation"
    URBAN_PLANNING = "urban_planning"


DOCUMENT_CATEGORIES = [
    document_category.value for document_category in DocumentCategory
]


class DocumentKeywords(ConfigModel):
    document_types: list[DocumentType] = []
    document_categories: list[DocumentCategory] = []
    entity_ids: list[str] = []
    document_date: datetime | None = None

    def to_dict(self) -> dict:
        return {
            "document_types": [
                document_type.value for document_type in self.document_types
            ],
            "document_categories": [
                document_category.value
                for document_category in self.document_categories
            ],
            "entity_ids": self.entity_ids,
            "document_date": self.document_date,
        }

    @staticmethod
    def from_dict(keywords: dict | None):
        if not keywords:
            return None
        document_types = [
            DocumentType(value=document_type)
            for document_type in keywords.get("document_types")
            if document_type in DOCUMENT_TYPES
        ]
        document_categories = [
            DocumentCategory(value=document_category)
            for document_category in keywords.get("document_categories")
            if document_category in DOCUMENT_CATEGORIES
        ]
        entity_ids = [
            entity_id for entity_id in keywords.get("entity_ids") if entity_id
        ]
        document_date = (
            keywords.get("document_date") if keywords.get("document_date") else None
        )
        return DocumentKeywords(
            document_types=document_types,
            document_categories=document_categories,
            entity_ids=entity_ids,
            document_date=document_date,
        )


class Document(ConfigModel):
    id: str
    project_id: str
    name: str
    slug: str
    path: str
    url: str
    content_type: str
    size: float
    source_url: str | None = None
    conversation_id: str | None
    user_id: str | None = None
    external_id: str | None = None
    metadata: str | None = None  # Context string
    keywords: DocumentKeywords | None = None
    nodes: list[TextNode] = []
    created_at: datetime = datetime.now(utc)


class DocumentTask(ConfigModel):
    document: Document
    task: Task


class DocumentMetadata(BaseModel):
    """Document Metadata"""

    title: str = Field(
        description="A short readable document name",
    )
    slug: str = Field(
        description="A unique identifier for the document based on the name",
    )
    description: str = Field(
        description="A short 1 line description of the document",
    )
    project_name: str = Field(
        description="The name of the project",
    )
    keywords: list[str] = Field(
        description="10 important keywords in the document",
    )
    document_types: list[str] = Field(
        description=f"The types of document, you can add up to 3 different values from these: {[document_type.value for document_type in DocumentType]}",
    )
    document_categories: list[str] = Field(
        description=f"The categories of document, you can add up to 3 different values from these: {[document_category.value for document_category in DocumentCategory]}",
    )
    entity_ids: list[str] = Field(
        description="The ids found in the document text. It can be numeric, alphanumeric or UUID",
    )


class DocumentSortField(str, Enum):
    SIZE = "size"
    CREATED_AT = "created_at"


class DocumentResponse(ConfigModel):
    id: str
    name: str
    slug: str
    url: str
    content_type: str
    size: float
    source_url: str | None = None
    conversation_id: str | None
    user_id: str | None = None
    external_id: str | None = None
    created_at: datetime = datetime.now(utc)
    status: TaskStatus | None = None


class DocumentUsageResponse(ConfigModel):
    total_count: int
    total_size: float


class InsertDocumentInput(ConfigModel):
    project_id: str
    name: str
    slug: str
    path: str
    url: str
    content_type: str
    size: float
    source_url: str | None = None
    conversation_id: str | None
    user_id: str | None = None
    external_id: str | None = None
    metadata: str | None = None

    def to_dict(self) -> dict:
        conversation_id = (
            ObjectId(self.conversation_id) if self.conversation_id else None
        )
        return {
            "project_id": ObjectId(self.project_id),
            "name": self.name,
            "slug": self.slug,
            "path": self.path,
            "url": self.url,
            "content_type": self.content_type,
            "size": self.size,
            "source_url": self.source_url or None,
            "conversation_id": conversation_id or None,
            "user_id": ObjectId(self.user_id) if self.user_id else None,
            "external_id": self.external_id or None,
            "metadata": self.metadata or None,
            "keywords": None,
            "nodes": [],
            "created_at": datetime.now(utc),
        }


class PostDocumentBody(ConfigModel):
    url: str
    name: str | None = None
    source_url: str | None = None
    conversation_id: str | None = None
    external_id: str | None = None
    content_type: str | None = None
    metadata: str | None = None


class PostDocumentsBody(ConfigModel):
    documents: list[PostDocumentBody]


class PatchDocumentBody(ConfigModel):
    name: str | None = None
    source_url: str | None = None
    conversation_id: str | None = None
    user_id: str | None = None
    external_id: str | None = None
    metadata: str | None = None


class DeleteDocumentsBody(ConfigModel):
    document_ids: list[str] | None = None
    external_ids: list[str] | None = None
    user_id: str | None = None
    conversation_id: str | None = None


class CreateDocumentInput(ConfigModel):
    project_id: str
    name: str
    slug: str
    path: str
    content_type: str
    size: float
    url: str
    folder_id: str | None = None
    user_id: str | None = None
    conversation_id: str | None = None
    external_id: str | None = None
    source_url: str | None = None
    priority: TaskPriority = TaskPriority.LOW
    metadata: str | None = None
    keywords: DocumentKeywords | None = None
    nodes: list[TextNode] = []


class UpdateDocumentInput(ConfigModel):
    name: str | None = None
    source_url: str | None = None
    conversation_id: str | None = None
    user_id: str | None = None
    external_id: str | None = None
    metadata: str | None = None
    keywords: DocumentKeywords | None = None
    nodes: list[TextNode] = []
