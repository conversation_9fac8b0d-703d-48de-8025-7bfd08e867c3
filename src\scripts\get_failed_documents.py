import asyncio
import json
import os
from datetime import datetime

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient

client = AsyncIOMotorClient(os.environ.get("MONGO_URI"))
db = client["document_db"]
tasks_collection = db["tasks"]
documents_collection = db["documents"]
projects_collection = db["projects"]
organizations_collection = db["organizations"]


def convert_objectid(obj):
    if isinstance(obj, dict):
        return {key: convert_objectid(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_objectid(item) for item in obj]
    elif isinstance(obj, ObjectId):
        return str(obj)
    elif isinstance(obj, datetime):
        return obj.isoformat()
    return obj


async def run():
    document_tasks = await tasks_collection.find(
        {"document_id": {"$exists": True}, "status": "failed"}
    ).to_list()

    document_ids = [ObjectId(task["document_id"]) for task in document_tasks]
    documents = await documents_collection.find(
        {"_id": {"$in": document_ids}}
    ).to_list()

    project_ids = list(set([document["project_id"] for document in documents]))
    projects = await projects_collection.find({"_id": {"$in": project_ids}}).to_list()

    organization_ids = list(set([project["organization_id"] for project in projects]))
    organizations = await organizations_collection.find(
        {"_id": {"$in": organization_ids}}
    ).to_list()

    organization_project_map = {
        project["organization_id"]: project["_id"] for project in projects
    }

    results = {}
    for organization in organizations:
        organization_id = organization["_id"]
        clerk_id = organization["clerk_organization_id"]
        project_id = organization_project_map[organization_id]
        results[clerk_id] = {
            "organization_id": organization_id,
            "project_id": project_id,
            "documents": [
                document
                for document in documents
                if document["project_id"] == project_id
            ],
        }

    results = convert_objectid(results)

    with open("samples/failed_documents.json", "w") as f:
        json.dump(results, f, indent=2)


asyncio.run(run())
