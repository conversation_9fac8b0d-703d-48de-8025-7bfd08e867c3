from fastapi import HTTPException

from src.models.auth import Auth
from src.models.prompt import (
    DeletePromptsBody,
    InsertPromptInput,
    PatchPromptBody,
    PostPromptBody,
    Prompt,
    PromptResponse,
    UpdatePromptInput,
)
from src.models.shared import DeleteResponse, PaginationResponse
from src.repositories.prompt import PromptRepository


class PromptService:
    def __init__(self):
        self.prompt_repository = PromptRepository()

    async def post_one(
        self,
        auth: Auth,
        body: PostPromptBody,
    ) -> list[PromptResponse]:
        prompt = await self.prompt_repository.insert_one(
            input=InsertPromptInput(
                project_id=auth.project.id,
                user_id=auth.user.id,
                name=body.name,
                text=body.text,
            )
        )
        if not prompt:
            raise HTTPException(status_code=500, detail="Error creating prompt")
        return self._to_prompt_response(prompt)

    async def get_one(
        self,
        auth: Auth,
        prompt_id: str,
    ) -> PromptResponse:
        prompt = await self.prompt_repository.find_one(
            prompt_id=prompt_id,
            project_id=auth.project.id,
            user_id=auth.user.id,
        )
        if not prompt:
            raise HTTPException(status_code=404, detail="Prompt not found")
        return self._to_prompt_response(prompt)

    async def get_many(
        self,
        auth: Auth,
        prompt_ids: list[str] | None = None,
    ) -> list[PromptResponse]:
        prompts = await self.prompt_repository.find_many(
            prompt_ids=prompt_ids,
            project_id=auth.project.id,
            user_id=auth.user.id,
        )
        return [self._to_prompt_response(prompt) for prompt in prompts]

    async def paginate(
        self,
        auth: Auth,
        name: str | None = None,
        page_size: int = 10,
        page_number: int = 1,
    ) -> PaginationResponse[PromptResponse]:
        result = await self.prompt_repository.paginate(
            project_id=auth.project.id,
            user_id=auth.user.id,
            name=name,
            page_size=page_size,
            page_number=page_number,
        )

        return PaginationResponse(
            data=[self._to_prompt_response(prompt) for prompt in result.data],
            pagination=result.pagination,
        )

    async def patch_one(
        self,
        auth: Auth,
        prompt_id: str,
        body: PatchPromptBody,
    ) -> PromptResponse:
        prompt = await self.prompt_repository.update_one(
            prompt_id=prompt_id,
            project_id=auth.project.id,
            user_id=auth.user.id,
            input=UpdatePromptInput(
                name=body.name,
                text=body.text,
            ),
        )
        if not prompt:
            raise HTTPException(status_code=404, detail="Prompt not found")
        return self._to_prompt_response(prompt)

    async def delete_one(
        self,
        auth: Auth,
        prompt_id: str,
    ) -> DeleteResponse:
        prompt = await self.prompt_repository.delete_one(
            prompt_id=prompt_id,
            project_id=auth.project.id,
            user_id=auth.user.id,
        )
        if not prompt:
            raise HTTPException(status_code=404, detail="Prompt not found")
        return DeleteResponse(ids=[prompt.id], count=1)

    async def delete_many(
        self,
        auth: Auth,
        body: DeletePromptsBody,
    ) -> DeleteResponse:
        if not body.prompt_ids:
            raise HTTPException(status_code=400, detail="Prompt IDs are required")
        prompts = await self.prompt_repository.delete_many(
            prompt_ids=body.prompt_ids,
            project_id=auth.project.id,
            user_id=auth.user.id,
        )
        if not prompts:
            raise HTTPException(status_code=404, detail="Prompts not found")
        return DeleteResponse(ids=[prompt.id for prompt in prompts], count=len(prompts))

    def _to_prompt_response(self, prompt: Prompt) -> PromptResponse:
        return PromptResponse(
            id=prompt.id,
            user_id=prompt.user_id,
            name=prompt.name,
            text=prompt.text,
            created_at=prompt.created_at,
        )
