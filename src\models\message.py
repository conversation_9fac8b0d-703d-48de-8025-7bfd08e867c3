from datetime import datetime
from enum import Enum

from bson import ObjectId
from pytz import utc

from src.models.llm import LLMModel, LLMProvider
from src.models.shared import ConfigModel, DateCount
from src.util.llm_validation import validate_provider_model_compatibility


class MessageType(str, Enum):
    TEXT = "text"
    FILE = "file"
    OTHER = "other"


class DocumentMessage(ConfigModel):
    document_id: str
    name: str
    slug: str
    url: str
    size: float
    content_type: str
    source_url: str | None = None

    def to_dict(self) -> dict:
        return {
            "document_id": ObjectId(self.document_id),
            "name": self.name,
            "slug": self.slug,
            "url": self.url,
            "size": self.size,
            "content_type": self.content_type,
            "source_url": self.source_url,
        }


class Message(ConfigModel):
    id: str
    is_user: bool
    type: MessageType = MessageType.TEXT
    text: str | None = None
    documents: list[DocumentMessage] = []
    context_id: str | None = None
    created_at: datetime = datetime.now(utc)
    llm_provider: LLMProvider | None = None
    llm_model: LLMModel | None = None

    def to_dict(self) -> dict:
        provider, model = validate_provider_model_compatibility(
            self.llm_provider, self.llm_model
        )

        return {
            "_id": ObjectId(self.id),
            "is_user": self.is_user,
            "type": self.type,
            "text": self.text,
            "documents": [document.to_dict() for document in self.documents],
            "context_id": ObjectId(self.context_id) if self.context_id else None,
            "llm_provider": provider,
            "llm_model": model,
            "created_at": self.created_at,
        }


class UserQuery(ConfigModel):
    user_id: str
    conversation_id: str
    is_user: bool
    type: MessageType = MessageType.TEXT
    text: str | None = None
    documents: list[DocumentMessage] = []
    created_at: datetime = datetime.now(utc)


class CreateMessageBody(ConfigModel):
    is_user: bool
    text: str | None = None
    type: MessageType = MessageType.TEXT
    uploaded_document_ids: list[str] = []


class MessageUsageResponse(ConfigModel):
    total_count: int
    per_day: list[DateCount] = []


class DocumentMessageResponse(ConfigModel):
    document_id: str
    name: str
    slug: str
    url: str
    size: float
    content_type: str
    source_url: str | None = None


class MessageResponse(ConfigModel):
    id: str
    is_user: bool
    type: MessageType = MessageType.TEXT
    text: str | None = None
    documents: list[DocumentMessageResponse] = []
    context_id: str | None = None
    llm_provider: LLMProvider | None = None
    llm_model: LLMModel | None = None
    created_at: datetime = datetime.now(utc)


class InsertDocumentMessageInput(ConfigModel):
    document_id: str
    name: str
    slug: str
    url: str
    size: float
    content_type: str
    source_url: str | None = None

    def to_dict(self) -> dict:
        return {
            "document_id": ObjectId(self.document_id),
            "name": self.name,
            "slug": self.slug,
            "url": self.url,
            "size": self.size,
            "content_type": self.content_type,
            "source_url": self.source_url,
        }


class InsertMessageInput(ConfigModel):
    is_user: bool
    type: MessageType = MessageType.TEXT
    text: str | None = None
    documents: list[InsertDocumentMessageInput] = []
    created_at: datetime | None = None
    llm_provider: LLMProvider | None = None
    llm_model: LLMModel | None = None

    def to_dict(self) -> dict:
        provider, model = validate_provider_model_compatibility(
            self.llm_provider, self.llm_model
        )

        return {
            "_id": ObjectId(),
            "is_user": self.is_user,
            "type": self.type,
            "text": self.text,
            "documents": [document.to_dict() for document in self.documents],
            "context_id": None,
            "llm_provider": provider,
            "llm_model": model,
            "created_at": self.created_at or datetime.now(utc),
        }


class UpdateMessageInput(ConfigModel):
    context_id: str | None = None

    def to_dict(self) -> dict:
        return {
            "context_id": ObjectId(self.context_id) if self.context_id else None,
        }
