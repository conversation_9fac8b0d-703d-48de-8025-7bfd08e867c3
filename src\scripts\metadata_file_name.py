import asyncio
import json
import os

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient


client = AsyncIOMotorClient(os.environ.get("MONGO_URI"))
db = client["document_db"]
documents_collection = db["documents"]


async def get_documents():
    documents_with_metadata = await documents_collection.find(
        {"metadata": {"$ne": None}}
    ).to_list()

    document_file_names = {}
    for document in documents_with_metadata:
        metadata = document.get("metadata", "")
        if metadata and "userFileName" in metadata:
            metadata_dict = json.loads(metadata)
            metadata_file_name = metadata_dict.get("userFileName")
            document_id = str(document.get("_id"))
            file_name = document.get("name")
            if metadata_file_name and metadata_file_name != file_name:
                document_file_names[document_id] = metadata_file_name

    return document_file_names


async def update_document(document_id: str, file_name: str):
    await documents_collection.update_one(
        {"_id": ObjectId(document_id)},
        {"$set": {"name": file_name}},
    )


async def main():
    documents = await get_documents()
    print(f"Found {len(documents)} documents to update file name")
    for document_id, file_name in documents.items():
        print(f"Updating document {document_id} with file name {file_name}")
        await update_document(document_id, file_name)
    print("Done")


if __name__ == "__main__":
    asyncio.run(main())
