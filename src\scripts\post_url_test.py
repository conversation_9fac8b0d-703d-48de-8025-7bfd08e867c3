import json
import urllib.request
import urllib.parse

url = "https://apidev.snapzai.com/documents/urls"
# url = "https://query-engine-dev-v9uej.ondigitalocean.app/documents/urls"

headers = {
    "accept": "application/json",
    "X-API-Key": "snapz-dev-2b9a0217-740b-4d9d-908b-b04920c83131-bLaR-eiPo1txIcfFbgtVS9YMKCVH8td-nJXJNHWoT9U",
    "Content-Type": "application/json",
}

data = {
    "documents": [
        {
            "url": "https://devsav3files.blob.core.windows.net/project-f8914e75-bdc9-4fa4-9193-a283872a7ddd/1c8126b1-0193-49b8-9bce-8ea8cb321f2a_1..pdf?sv=2024-08-04&se=2024-11-13T10%3A03%3A43Z&sr=b&sp=r&rscd=attachment%3B+filename%3D%2218.416+Cielo+Vista_1.pdf%22%3B+filename*%3DUTF-8%27%2718.416%2520Cielo%2520Vista_1.pdf&sig=HbCSSoGihGlcsSOVUDnKNYt9bqm9GHqNHmoZscGSKzI%3D",
            "name": "owner-insite-file.pdf",
            "sourceUrl": "string",
            "userId": "string",
            "externalId": "string",
            "contentType": "application/pdf",
            "metadata": "string",
        }
    ]
}

# Convert data to JSON
data_json = json.dumps(data).encode("utf-8")

# Create request object
req = urllib.request.Request(url, data=data_json, headers=headers)

try:
    # Send request
    with urllib.request.urlopen(req) as response:
        response_text = response.read().decode("utf-8")
        print(response_text)

except urllib.error.HTTPError as e:
    print(f"HTTP Error: {e.code} - {e.reason}")
except urllib.error.URLError as e:
    print(f"URL Error: {e.reason}")
