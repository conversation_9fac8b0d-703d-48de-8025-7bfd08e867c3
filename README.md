# Snapz.ai Document Processing API

Copyright 2024 Cognitv Corporation

## Overview

Snapz.ai is a sophisticated document processing API built with Python that leverages advanced AI capabilities for document intake, analysis, and querying. The system implements a Retrieval-Augmented Generation (RAG) architecture to provide intelligent document processing and information retrieval.

## Key Features

- Document intake and processing
- AI-powered document summarization
- Natural language querying of document content
- Secure user authentication and authorization
- Asynchronous task processing
- Scalable document storage and retrieval

## Technology Stack

- **Backend Framework**: FastAPI
- **AI/ML Components**:
  - LlamaIndex for document indexing
  - OpenAI LLMs for embeddings and response generation
  - Cohere Rerank for result optimization
- **Databases**:
  - MongoDB for document storage
  - Pinecone for vector storage
- **Authentication**: JWT-based authentication
- **Container Platform**: Docker

## Background tasks and jobs

The application uses the `apscheduler` library to run background tasks and jobs.
It is initialized in the `src/worker.py` file.

### Jobs

Jobs are cron jobs that are run on a schedule or inmediately, whenever the user requests it.
They are scalable and there is no limit to the number of them.
They allow to schedule tasks to run in the future in a flexible way.

### Tasks

Tasks are fetched from cronjobs and run periodically with a clear limit.
They are not scalable and there is a limit to the number of them run at the same time.
They are used to run tasks that are not time-sensitive or that need to have memory limitations.

## Prerequisites

- Python 3.8+
- Docker and Docker Compose
- MongoDB
- API keys for:
  - OpenAI
  - Pinecone
  - Cohere (optional)

## Installation

1. Clone the repository:

   ```bash
   git clone [repository-url]
   cd DocProjectAPI
   ```

2. Install dependencies using Pipenv:

   ```bash
   pipenv install
   ```

3. Set up environment variables:
   Create a `.env` file with the following variables:
   ```
   MONGODB_URL=mongodb://localhost:xxxxx
   OPENAI_API_KEY=your_openai_key
   PINECONE_API_KEY=your_pinecone_key
   COHERE_API_KEY=your_cohere_key
   JWT_SECRET=your_jwt_secret
   ```

## Running the Application

### Using Docker Compose

1. Start the services:
   ```bash
   docker-compose up -d
   ```

This will start:

- MongoDB on port xxxxx

### Fixing the Hostname in the Replica Set Configuration

The replica set is configured to use localhost, which is inaccessible from other containers. You need to reconfigure the replica set to use the hostname `mongodb`.

1. Connect to the MongoDB Shell:

```bash
docker exec -it <mongodb_container_id> bash
mongosh
```

2. Reconfigure the Replica Set:

```javascript
// Fetch current replica set configuration
cfg = rs.conf();

// Update hostname
cfg.members[0].host = "mongodb:27017";

// Apply changes
rs.reconfig(cfg, { force: true });
```

3. Verify the Update:

```javascript
rs.status();
```

Ensure that the hostname now shows `mongodb:27017` instead of `localhost:27017`.

### Running Locally

1. Activate the virtual environment:

   ```bash
   pipenv shell
   ```

2. Run the FastAPI application:
   ```bash
   uvicorn src.api:app --reload --port 8080
   ```

## API Documentation

Once the application is running, access the API documentation at:

- Swagger UI: `http://localhost:8080/docs`
- ReDoc: `http://localhost:8080/redoc`

## Project Structure

```
DocProjectAPI/
├── src/
│   ├── controllers/    # Request handlers
│   ├── middlewares/    # Custom middleware
│   ├── models/         # Data models
│   ├── repositories/   # Data access layer
│   ├── services/       # Business logic
│   ├── tasks/          # Async tasks
│   └── util/           # Utility functions
├── tests/              # Test suite
└── docker-compose.yaml # Container orchestration
```

## Authentication

The API uses JWT-based authentication. To access protected endpoints:

1. Register a new user at `/user/register`
2. Obtain a JWT token at `/user/login`
3. Include the token in the Authorization header: `Bearer <token>`

## Release Process

Once your PR is merged to main, you can run the following command to deploy the frontend:

Automated release to dev environment:
```bash
pipenv run release-dev
```

Manual release to prod environment:
```bash
git checkout main
git pull
git checkout release/prod
git reset --hard origin/main
git push
git checkout main
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

Copyright 2024 Cognitv Corporation. All rights reserved.
