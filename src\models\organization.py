from datetime import datetime

from pytz import utc

from src.models.clerk import ClerkOrganization
from src.models.shared import ConfigModel


class OrganizationApiKey(ConfigModel):
    api_key: str
    created_at: datetime = datetime.now(utc)


class Organization(ConfigModel):
    id: str
    clerk_organization_id: str
    clerk: ClerkOrganization | None = None
    api_keys: list[OrganizationApiKey] = []
    created_at: datetime = datetime.now(utc)


class InsertOrganizationInput(ConfigModel):
    clerk_organization_id: str
    api_key: str

    def to_dict(self) -> dict:
        return {
            "clerk_organization_id": self.clerk_organization_id,
            "api_keys": [
                {
                    "api_key": self.api_key,
                    "created_at": datetime.now(utc),
                }
            ],
            "created_at": datetime.now(utc),
        }
