from fastapi import APIRouter, Form, Query, Request, UploadFile

from src.models.document import (
    ContentType,
    DeleteDocumentsBody,
    DocumentResponse,
    DocumentSortField,
    DocumentUsageResponse,
)
from src.models.shared import DeleteResponse, PaginationResponse, SortOrder
from src.services.document import DocumentService

document_router = APIRouter()
document_service = DocumentService()


@document_router.post("/files", response_model=list[DocumentResponse])
async def post_files(
    request: Request,
    files: list[UploadFile],
    source_url: str | None = Form(None),
    conversation_id: str | None = Form(None),
    external_id: str | None = Form(None),
    metadata: str | None = Form(None),
):
    return await document_service.post_files(
        auth=request.state.auth,
        files=files,
        source_url=source_url,
        conversation_id=conversation_id,
        external_id=external_id,
        metadata=metadata,
    )


@document_router.get("/usage", response_model=DocumentUsageResponse)
async def document_usage(request: Request, user_id: str | None = None):
    return await document_service.usage(auth=request.state.auth, user_id=user_id)


@document_router.get("/paginate", response_model=PaginationResponse[DocumentResponse])
async def paginate(
    request: Request,
    name: str | None = None,
    document_ids: list[str] = Query([]),
    external_ids: list[str] = Query([]),
    conversation_id: str | None = None,
    user_id: str | None = None,
    size: float | None = None,
    content_type: ContentType | None = None,
    start_date: str | None = None,
    end_date: str | None = None,
    page_size: int = 10,
    page_number: int = 1,
    sort_by: DocumentSortField | None = None,
    sort_order: SortOrder | None = None,
):
    return await document_service.paginate(
        auth=request.state.auth,
        document_ids=document_ids,
        external_ids=external_ids,
        name=name,
        conversation_id=conversation_id,
        user_id=user_id,
        size=size,
        content_type=content_type,
        start_date=start_date,
        end_date=end_date,
        page_size=page_size,
        page_number=page_number,
        sort_by=sort_by,
        sort_order=sort_order,
    )


@document_router.get("/{document_id}", response_model=DocumentResponse)
async def get_one(request: Request, document_id: str):
    return await document_service.get_one(
        auth=request.state.auth,
        document_id=document_id,
    )


@document_router.delete("", response_model=DeleteResponse)
async def delete_many(request: Request, body: DeleteDocumentsBody):
    return await document_service.delete_many(
        auth=request.state.auth,
        body=body,
    )
