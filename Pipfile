[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
fastapi = "0.115.5"
pydantic = {extras = ["email"], version = "2.9.2"}
uvicorn = {extras = ["standard"], version = "0.32.1"}
pymongo = "4.9.2"
motor = "3.6.0"
python-jose = {extras = ["cryptography"], version = "3.3.0"}
python-multipart = "0.0.18"
websockets = "14.1.0"
pyhumps = "3.8.0"
openai = "1.75.0"
llama-index = "0.12.31"
llama-index-core = "0.12.31"
llama-index-llms-openai = "0.3.37"
llama-index-llms-gemini = "0.4.14"
llama-index-embeddings-openai = "0.3.1"
llama-index-embeddings-gemini = "0.3.2"
llama-index-llms-groq = "0.3.1"
llama-index-llms-anthropic = "0.8.0"
llama-index-program-openai = "0.3.1"
llama-index-vector-stores-pinecone = "0.4.5"
llama-index-postprocessor-cohere-rerank = "0.3.0"
llama-index-tools-tavily-research = "0.3.0"
llama-parse = "0.6.12"
arize-otel = "0.8.1"
opentelemetry-exporter-otlp = "1.32.0"
openinference-instrumentation-llama-index = "4.2.1"
sentry-sdk = "2.20.0"
transformers = "4.46.3"
tensorflow = "2.18.0"
aioboto3 = "13.2.0"
aiohttp = "3.11.8"
requests = "2.32.3"
apscheduler = "3.10.4"
reportlab = "*"

[dev-packages]

[requires]
python_version = "3.11"

[scripts]
api = "uvicorn src.api:app --host 0.0.0.0 --port 8080"
worker = "uvicorn src.worker:app --host 0.0.0.0 --port 8081"
release-dev = "sh -c 'git checkout main && git pull && git checkout release/dev && git reset --hard origin/main && git push && git checkout main'"
