from src.models.conversation import DocumentScore
from src.models.shared import ConfigModel


class ChatMessageFilter(ConfigModel):
    document_ids: list[str] | None = None


class ChatMessageRequest(ConfigModel):
    conversation_id: str
    text: str
    filters: ChatMessageFilter | None = None
    web_search: bool = False


class ChatMessageResponse(ConfigModel):
    conversation_id: str
    end: bool = False
    text: str | None = None
    scores: list[DocumentScore] | None = None
