from fastapi import APIRouter, Request

from src.models.settings import SettingsResponse, UpdateSettingsBody
from src.services.settings import SettingsService

settings_router = APIRouter()
settings_service = SettingsService()


@settings_router.get("", response_model=SettingsResponse | None)
async def get_settings(request: Request):
    return await settings_service.get_one(
        auth=request.state.auth,
    )


@settings_router.patch("", response_model=SettingsResponse)
async def update_settings(request: Request, body: UpdateSettingsBody):
    return await settings_service.update_one(
        auth=request.state.auth,
        body=body,
    ) 