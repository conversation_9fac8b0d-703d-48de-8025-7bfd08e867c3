from email.mime.application import MIMEApplication
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
import os
import smtplib

from src.util.logger import Logger


class EmailService:
    def __init__(self):
        self.logger = Logger("EmailService")
        self.smtp_server = os.environ.get("SMTP_SERVER")
        self.smtp_port = int(os.environ.get("SMTP_PORT"))
        self.smtp_username = os.environ.get("SMTP_USERNAME")
        self.smtp_password = os.environ.get("SMTP_PASSWORD")
        self.sender_email = os.environ.get("SENDER_EMAIL")

    async def send_email(
        self,
        recipient_email: str,
        subject: str,
        body: str,
        attachment_data: bytes = None,
        attachment_name: str = None,
    ) -> bool:
        if not self.smtp_username or not self.smtp_password:
            self.logger.error("SMTP credentials not configured")
            return False

        try:
            message = MIMEMultipart()
            message["From"] = self.sender_email
            message["To"] = recipient_email
            message["Subject"] = subject

            message.attach(MIMEText(body, "plain"))

            if attachment_data and attachment_name:
                attachment = MIMEApplication(attachment_data)
                attachment.add_header(
                    "Content-Disposition", f"attachment; filename={attachment_name}"
                )
                message.attach(attachment)

            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(message)

            self.logger.info(f"Email sent successfully to {recipient_email}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to send email: {str(e)}")
            return False
