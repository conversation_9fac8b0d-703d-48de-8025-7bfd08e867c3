from datetime import datetime
from enum import Enum

from bson import ObjectId
from pytz import utc

from src.models.shared import ConfigModel
from src.models.llm import LLMProvider, LLMModel
from src.util.llm_validation import validate_provider_model_compatibility


class UpdateSettingsBody(ConfigModel):
    llm_provider: LLMProvider
    llm_model: LLMModel


class Settings(ConfigModel):
    id: str
    project_id: str
    user_id: str | None = None
    llm_provider: LLMProvider
    llm_model: LLMModel
    created_at: datetime


class SettingsResponse(ConfigModel):
    id: str
    llm_provider: LLMProvider
    llm_model: LLMModel
    created_at: datetime


class InsertSettingsInput(ConfigModel):
    project_id: str
    user_id: str
    llm_provider: LLMProvider = LLMProvider.OPENAI
    llm_model: LLMModel = LLMModel.GPT_4_1_MINI
    created_at: datetime = datetime.now(utc)

    def to_dict(self):
        provider, model = validate_provider_model_compatibility(
            self.llm_provider, self.llm_model
        )
        return {
            "project_id": ObjectId(self.project_id),
            "user_id": ObjectId(self.user_id),
            "llm_provider": provider,
            "llm_model": model,
            "created_at": self.created_at,
        }


class UpdateSettingsInput(ConfigModel):
    llm_provider: LLMProvider | None = None
    llm_model: LLMModel | None = None
