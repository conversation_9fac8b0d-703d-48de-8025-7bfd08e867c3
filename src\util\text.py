import re
from uuid import uuid4


def slugify_file_name(file_name: str) -> str:
    dot_parts = file_name.split(".")
    if len(dot_parts) == 1:
        return slugify(file_name)
    extension = dot_parts[-1]
    full_name = dot_parts[0]
    full_name_parts = full_name.split("/")
    base_name = full_name_parts[-1] if len(full_name_parts) > 1 else full_name
    if base_name and extension:
        slug = slugify(base_name)
        return f"{slug}.{extension}"
    return slugify(file_name)


def slugify(text: str) -> str:
    return re.sub(r"\W+", "_", text).lower() + "_" + uuid4().hex[:8]
