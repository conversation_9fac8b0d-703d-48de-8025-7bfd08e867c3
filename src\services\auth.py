import asyncio
import os
import secrets
import uuid

from fastapi import HTTPException
from jose import jwt

from src.models.auth import Auth, TokenData
from src.models.organization import InsertOrganizationInput, Organization
from src.models.project import InsertProjectInput, Project
from src.models.prompt import InsertPromptInput
from src.models.settings import InsertSettingsInput, Settings
from src.models.user import InsertUserInput, User
from src.repositories.organization import OrganizationRepository
from src.repositories.project import ProjectRepository
from src.repositories.prompt import PromptRepository
from src.repositories.settings import SettingsRepository
from src.repositories.user import UserRepository
from src.services.clerk import ClerkService
from src.util.logger import Logger
from src.util.text import slugify

ALGORITHM = "RS256"
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY")


class AuthService:
    def __init__(self):
        self.logger = Logger("AuthService")
        self.organization_repository = OrganizationRepository()
        self.project_repository = ProjectRepository()
        self.user_repository = UserRepository()
        self.clerk_service = ClerkService()
        self.prompt_repository = PromptRepository()
        self.settings_repository = SettingsRepository()

    async def authenticate_by_api_key(self, api_key: str) -> Auth:
        organization = await self.organization_repository.find_one(api_key=api_key)
        if not organization:
            raise HTTPException(status_code=401, detail="Invalid API key")
        project = await self.project_repository.find_one(
            organization_id=organization.id
        )
        if not project:
            raise HTTPException(status_code=401, detail="Organization not initialized")
        return Auth(organization=organization, project=project)

    async def authenticate_by_token(self, token: str) -> Auth:
        token_data = self.decode_token(token)
        if not token_data:
            raise HTTPException(status_code=401, detail="Invalid token")
        organization = await self.get_or_create_organization(
            token_data.clerk_organization_id
        )
        project = await self.get_or_create_project(organization.id)
        user = await self.get_or_create_user(token_data.clerk_user_id, project.id)
        settings = await self.get_or_create_settings(project.id, user.id)
        return Auth(
            user=user,
            organization=organization,
            project=project,
            settings=settings,
        )

    async def get_or_create_organization(
        self, clerk_organization_id: str
    ) -> Organization:
        (organization, clerk_organization) = await asyncio.gather(
            self.organization_repository.find_one(
                clerk_organization_id=clerk_organization_id
            ),
            self.clerk_service.get_organization(clerk_organization_id),
        )
        if not clerk_organization:
            raise HTTPException(status_code=404, detail="Clerk organization not found")
        if not organization:
            organization = await self.organization_repository.upsert_one(
                input=InsertOrganizationInput(
                    clerk_organization_id=clerk_organization_id,
                    api_key=self.generate_api_key(),
                )
            )
        organization.clerk = clerk_organization
        return organization

    async def get_or_create_user(
        self, clerk_user_id: str, project_id: str | None = None
    ) -> User:
        (user, clerk_user, memberships) = await asyncio.gather(
            self.user_repository.find_one(clerk_user_id=clerk_user_id),
            self.clerk_service.get_user(clerk_user_id),
            self.clerk_service.get_user_memberships(clerk_user_id),
        )
        if not clerk_user:
            raise HTTPException(status_code=404, detail="Clerk user not found")
        if not user:
            user = await self.user_repository.upsert_one(
                input=InsertUserInput(clerk_user_id=clerk_user_id)
            )
            if project_id:
                await asyncio.gather(
                    self.prompt_repository.insert_one(
                        input=InsertPromptInput(
                            project_id=project_id,
                            user_id=user.id,
                            name="RIF Summary",
                            text="Find RFIs for <material or company or status or date>. Analyze them and provide key data in a table.",
                        )
                    ),
                    self.prompt_repository.insert_one(
                        input=InsertPromptInput(
                            project_id=project_id,
                            user_id=user.id,
                            name="Submittal Summary",
                            text="Find Submittals for <material or company or status or date>. Analyze them and provide key data in a table.",
                        )
                    ),
                    self.prompt_repository.insert_one(
                        input=InsertPromptInput(
                            project_id=project_id,
                            user_id=user.id,
                            name="Change Order Summary",
                            text="Find Change Orders for <material or company or status or date>. Analyze them and provide key data in a table.",
                        )
                    ),
                )
        user.clerk = clerk_user
        user.memberships = memberships
        return user

    async def get_or_create_project(self, organization_id: str) -> Project:
        project = await self.project_repository.find_one(
            organization_id=organization_id
        )
        if not project:
            name = "Default project"
            project = await self.project_repository.insert_one(
                input=InsertProjectInput(
                    organization_id=organization_id,
                    name=name,
                    slug=slugify(name),
                )
            )
        return project

    async def get_or_create_settings(self, project_id: str, user_id: str) -> Settings:
        settings = await self.settings_repository.find_one(
            project_id=project_id,
            user_id=user_id,
        )
        if not settings:
            settings = await self.settings_repository.insert_one(
                input=InsertSettingsInput(
                    project_id=project_id,
                    user_id=user_id,
                )
            )
        return settings

    def generate_api_key(self) -> str:
        key = str(uuid.uuid4()) + "-" + secrets.token_urlsafe(32)
        env = os.getenv("ENV", "local")
        if env == "development":
            return "snapz-dev-" + key
        if env == "staging":
            return "snapz-stg-" + key
        if env == "production":
            return "snapz-prod-" + key
        return "snapz-local-" + key

    def decode_token(self, token: str) -> TokenData | None:
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[ALGORITHM])
            return TokenData(
                clerk_user_id=payload.get("sub"),
                clerk_organization_id=payload.get("org_id"),
                organization_role=payload.get("org_role"),
            )
        except jwt.JWTError as e:
            self.logger.error("Error decoding token", e)
            return None
