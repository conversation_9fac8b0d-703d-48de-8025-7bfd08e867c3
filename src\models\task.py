from datetime import datetime
from enum import Enum

from bson import ObjectId
from pytz import utc

from src.models.shared import ConfigModel


class TaskStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    PROCESSING = "processing"
    DONE = "done"
    FAILED = "failed"


class TaskPriority(int, Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3


class TaskAction(str, Enum):
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"


class TaskType(str, Enum):
    CONTEXT = "context"
    DOCUMENT = "document"


class TaskError(str, Enum):
    ERROR = "error"
    DOCUMENT_NOT_FOUND = "document_not_found"
    CONTEXT_NOT_FOUND = "context_not_found"
    PROJECT_NOT_FOUND = "project_not_found"
    CANNOT_DOWNLOAD = "cannot_download"
    NO_CONTENT = "no_content"
    CANNOT_PARSE = "cannot_parse"
    NO_PARSED_CONTENT = "no_parsed_content"
    CANNOT_INDEX = "cannot_index"
    NO_EMBEDDINGS = "no_embeddings"


class Task(ConfigModel):
    id: str
    document_id: str | None = None
    context_id: str | None = None
    status: TaskStatus = TaskStatus.PENDING
    priority: TaskPriority = TaskPriority.LOW
    action: TaskAction = TaskAction.CREATE
    duration: float | None = None
    error: TaskError | None = None
    error_message: str | None = None
    reset_count: int = 0
    created_at: datetime = datetime.now(utc)
    updated_at: datetime = datetime.now(utc)


class InsertTaskInput(ConfigModel):
    document_id: str | None = None
    context_id: str | None = None
    status: TaskStatus = TaskStatus.PENDING
    priority: TaskPriority = TaskPriority.LOW
    action: TaskAction = TaskAction.CREATE

    def to_dict(self) -> dict:
        document_id = ObjectId(self.document_id) if self.document_id else None
        context_id = ObjectId(self.context_id) if self.context_id else None
        return {
            "document_id": document_id or None,
            "context_id": context_id or None,
            "status": self.status,
            "priority": self.priority,
            "action": self.action,
            "duration": None,
            "error": None,
            "error_message": None,
            "reset_count": 0,
            "created_at": datetime.now(utc),
            "updated_at": datetime.now(utc),
        }
