from enum import Enum

from src.models.shared import ConfigModel


class ClerkOrganizationRole(str, Enum):
    ADMIN = "org:admin"
    MEMBER = "org:member"


class ClerkOrganization(ConfigModel):
    id: str
    name: str
    slug: str


class ClerkUserMembership(ConfigModel):
    organization: ClerkOrganization
    role: ClerkOrganizationRole


class ClerkUser(ConfigModel):
    id: str
    first_name: str
    last_name: str
    email: str 
