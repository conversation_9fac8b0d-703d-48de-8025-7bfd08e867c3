import asyncio
from datetime import datetime, timedelta

from bson import ObjectId

from src.models.message import (
    DocumentMessage,
    Message,
    MessageType,
    MessageUsageResponse,
    UserQuery,
)
from src.models.shared import Pagination, PaginationResponse, PaginationResult
from src.util.date import mongo_date
from src.util.llm_validation import validate_provider_model_compatibility
from src.util.mongo import db


class MessageRepository:
    def __init__(self):
        self.collection = db.get_collection("conversations")

    def _to_model(self, message_dict: dict) -> Message:
        provider, model = validate_provider_model_compatibility(
            message_dict.get("llm_provider"),
            message_dict.get("llm_model"),
        )

        context_id = message_dict.get("context_id")
        if context_id:
            context_id = str(context_id)

        return Message(
            id=str(message_dict.get("_id")),
            is_user=message_dict.get("is_user"),
            type=message_dict.get("type"),
            text=message_dict.get("text"),
            documents=[
                DocumentMessage(
                    document_id=str(document.get("document_id")),
                    name=document.get("name"),
                    slug=document.get("slug"),
                    url=document.get("url"),
                    size=document.get("size"),
                    content_type=document.get("content_type"),
                    source_url=document.get("source_url"),
                )
                for document in message_dict.get("documents")
            ],
            context_id=context_id,
            llm_provider=provider,
            llm_model=model,
            created_at=message_dict.get("created_at"),
        )

    def _to_user_query_model(self, user_query_dict: dict) -> UserQuery:
        return UserQuery(
            user_id=str(user_query_dict.get("user_id")),
            conversation_id=str(user_query_dict.get("conversation_id")),
            is_user=user_query_dict.get("is_user"),
            type=user_query_dict.get("type"),
            text=user_query_dict.get("text"),
            documents=[
                DocumentMessage(
                    document_id=str(document.get("document_id")),
                    name=document.get("name"),
                    slug=document.get("slug"),
                    url=document.get("url"),
                    size=document.get("size"),
                    content_type=document.get("content_type"),
                    source_url=document.get("source_url"),
                )
                for document in user_query_dict.get("documents")
            ],
            created_at=user_query_dict.get("created_at"),
        )

    async def paginate(
        self,
        project_id: str,
        user_id: str,
        page_size: int,
        page_number: int,
    ) -> list[UserQuery]:
        filter = {"project_id": ObjectId(project_id)}
        if user_id:
            filter["user_id"] = ObjectId(user_id)

        total_aggregation_cursor = self.collection.aggregate(
            [
                {
                    "$match": filter,
                },
                {
                    "$unwind": "$messages",
                },
                {
                    "$match": {
                        "$and": [
                            {"messages.is_user": True},
                            {"messages.type": MessageType.TEXT},
                            {"messages.text": {"$ne": None}},
                            {"messages.text": {"$ne": ""}},
                        ],
                    },
                },
                {
                    "$count": "total",
                },
            ]
        )

        pagination = Pagination(page_size=page_size, page_number=page_number)
        result_aggregation_cursor = self.collection.aggregate(
            [
                {
                    "$match": filter,
                },
                {
                    "$unwind": "$messages",
                },
                {
                    "$match": {
                        "$and": [
                            {"messages.is_user": True},
                            {"messages.type": MessageType.TEXT},
                            {"messages.text": {"$ne": None}},
                            {"messages.text": {"$ne": ""}},
                        ],
                    },
                },
                {
                    "$sort": {"messages.created_at": -1},
                },
                {
                    "$skip": pagination.skip(),
                },
                {
                    "$limit": pagination.limit(),
                },
                {
                    "$project": {
                        "_id": 1,
                        "user_id": "$user_id",
                        "conversation_id": "$_id",
                        "is_user": "$messages.is_user",
                        "type": "$messages.type",
                        "text": "$messages.text",
                        "documents": "$messages.documents",
                        "created_at": "$messages.created_at",
                    }
                },
            ]
        )

        (total_aggregation, result_aggregation) = await asyncio.gather(
            total_aggregation_cursor.to_list(length=1),
            result_aggregation_cursor.to_list(),
        )

        user_queries = [
            self._to_user_query_model(user_query_dict)
            for user_query_dict in result_aggregation
        ]
        total = int(total_aggregation[0]["total"]) if len(total_aggregation) else 0

        return PaginationResponse(
            data=user_queries,
            pagination=PaginationResult(
                page_size=pagination.page_size,
                page_number=pagination.page_number,
                total=total,
            ),
        )

    async def usage(
        self,
        project_id: str,
        user_id: str | None = None,
    ) -> MessageUsageResponse:
        filter = {"project_id": ObjectId(project_id)}
        if user_id:
            filter["user_id"] = ObjectId(user_id)

        pipeline = [
            {
                "$match": filter,
            },
            {
                "$unwind": "$messages",
            },
            {
                "$match": {
                    "$and": [
                        {"messages.is_user": True},
                        {"messages.type": MessageType.TEXT},
                        {"messages.text": {"$ne": None}},
                        {"messages.text": {"$ne": ""}},
                    ],
                },
            },
            {
                "$facet": {
                    "total_count": [{"$count": "count"}],
                    "per_day": [
                        {
                            "$match": {
                                "messages.created_at": {
                                    "$gte": datetime.now() - timedelta(days=30)
                                }
                            }
                        },
                        {
                            "$group": {
                                "_id": {
                                    "$dateToString": {
                                        "format": "%Y-%m-%d",
                                        "date": "$messages.created_at",
                                    }
                                },
                                "count": {"$sum": 1},
                            }
                        },
                        {"$sort": {"_id": -1}},
                        {"$project": {"date": "$_id", "count": 1, "_id": 0}},
                    ],
                }
            },
        ]

        result = await self.collection.aggregate(pipeline).to_list(1)

        if not result or not result[0]["total_count"]:
            return MessageUsageResponse(total_count=0, per_day=[])

        return MessageUsageResponse(
            total_count=result[0]["total_count"][0]["count"],
            per_day=result[0]["per_day"],
        )

    async def find_many_user_queries(
        self,
        project_id: str,
        user_id: str | None = None,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
    ) -> list[UserQuery]:
        conversation_filter = {"project_id": ObjectId(project_id)}
        if user_id:
            conversation_filter["user_id"] = ObjectId(user_id)

        message_filter = {"messages.is_user": True}

        date_filter = {}
        if start_date:
            date_filter["$gte"] = mongo_date(start_date)
        if end_date:
            date_filter["$lte"] = mongo_date(end_date)
        if date_filter:
            message_filter["messages.created_at"] = date_filter

        results = await self.collection.aggregate(
            [
                {
                    "$match": conversation_filter,
                },
                {
                    "$unwind": "$messages",
                },
                {"$match": message_filter},
                {
                    "$sort": {"messages.created_at": -1},
                },
                {
                    "$project": {
                        "_id": 1,
                        "user_id": "$user_id",
                        "conversation_id": "$_id",
                        "is_user": "$messages.is_user",
                        "text": "$messages.text",
                        "type": "$messages.type",
                        "documents": "$messages.documents",
                        "created_at": "$messages.created_at",
                    }
                },
            ]
        ).to_list(length=None)

        if not results:
            return []

        return [self._to_user_query_model(result) for result in results]
