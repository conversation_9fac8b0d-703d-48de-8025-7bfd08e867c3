from src.models.shared import PaginationResponse
from src.models.auth import Auth
from src.repositories.message import MessageRepository
from src.models.message import MessageUsageResponse, UserQuery


class MessageService:
    def __init__(self):
        self.message_repository = MessageRepository()

    async def paginate_user_queries(
        self,
        auth: Auth,
        user_id: str | None = None,
        page_size: int = 10,
        page_number: int = 1,
    ) -> PaginationResponse[UserQuery]:
        return await self.message_repository.paginate(
            project_id=auth.project.id,
            user_id=auth.get_member_user_id(user_id),
            page_size=page_size,
            page_number=page_number,
        )

    async def usage(
        self,
        auth: Auth,
        user_id: str | None = None,
    ) -> MessageUsageResponse:
        return await self.message_repository.usage(
            project_id=auth.project.id,
            user_id=auth.get_member_user_id(user_id),
        )
