from datetime import datetime
from typing import Any, Callable

from apscheduler.job import Job
from apscheduler.jobstores.mongodb import MongoDBJobStore
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from fastapi import HTTPException
from pytz import utc

from src.models.auth import Auth
from src.models.document import DOCUMENT_TYPES, DocumentType
from src.models.job_metadata import (
    VALID_PERIODS_PER_JOB_TYPE,
    CreateJobMetadataBody,
    DeleteJobMetadataBody,
    JobMetadata,
    JobMetadataResponse,
    JobType,
    PeriodUnit,
    UpdateJobMetadataBody,
)
from src.models.shared import DeleteResponse, PaginationResponse
from src.repositories.job_metadata import InsertJobMetadataInput, JobMetadataRepository
from src.util.logger import Logger
from src.util.scheduler import scheduler
from src.workflows.uploaded_document_summary import (
    UploadedDocumentSummaryInput,
    UploadedDocumentSummaryWorkflow,
)

uploaded_document_summary_workflow = UploadedDocumentSummaryWorkflow()


async def run_uploaded_document_summary(input: UploadedDocumentSummaryInput):
    return await uploaded_document_summary_workflow.run(input=input)


class JobMetadataService:
    def __init__(self):
        self.job_metadata_repository = JobMetadataRepository()
        self.logger = Logger("JobMetadataService")
        self.scheduler = scheduler

    def _to_job_metadata_response(
        self, job_metadata: JobMetadata
    ) -> JobMetadataResponse:
        job = self.scheduler.get_job(job_metadata.job_id)
        return JobMetadataResponse(
            id=job_metadata.id,
            user_id=job_metadata.user_id,
            name=job_metadata.name,
            created_at=job_metadata.created_at,
            type=job_metadata.type,
            period_unit=job_metadata.period_unit,
            period_value=job_metadata.period_value,
            metadata=job_metadata.metadata,
            next_run=job.next_run_time if job else None,
        )

    def create_job_id(self, type: JobType) -> str:
        match type:
            case JobType.UPLOADED_DOCUMENTS_SUMMARY:
                return f"uploaded_documents_summary_{datetime.now().isoformat()}"
            case _:
                raise HTTPException(status_code=400, detail=f"Invalid job type {type}")

    def add_cron_job(
        self,
        id: str,
        func: Callable,
        args: list[Any],
        period_unit: PeriodUnit,
        period_value: int,
    ) -> Job:
        trigger = "date" if period_unit == PeriodUnit.NOW else "interval"

        if trigger == "date":
            return self.scheduler.add_job(
                id=id,
                func=func,
                args=args,
                trigger=trigger,
                run_date=datetime.now(utc),
                max_instances=1,
                misfire_grace_time=30,
                coalesce=True,
                replace_existing=True,
            )
        else:
            match period_unit:
                case PeriodUnit.SECONDS:
                    return self.scheduler.add_job(
                        id=id,
                        func=func,
                        args=args,
                        trigger=trigger,
                        seconds=period_value,
                        max_instances=1,
                        misfire_grace_time=30,
                        coalesce=True,
                        replace_existing=True,
                    )
                case PeriodUnit.MINUTES:
                    return self.scheduler.add_job(
                        id=id,
                        func=func,
                        args=args,
                        trigger=trigger,
                        minutes=period_value,
                        max_instances=1,
                        misfire_grace_time=30,
                        coalesce=True,
                        replace_existing=True,
                    )
                case PeriodUnit.HOURS:
                    return self.scheduler.add_job(
                        id=id,
                        func=func,
                        args=args,
                        trigger=trigger,
                        hours=period_value,
                        max_instances=1,
                        misfire_grace_time=30,
                        coalesce=True,
                        replace_existing=True,
                    )
                case PeriodUnit.DAYS:
                    return self.scheduler.add_job(
                        id=id,
                        func=func,
                        args=args,
                        trigger=trigger,
                        days=period_value,
                        max_instances=1,
                        misfire_grace_time=30,
                        coalesce=True,
                        replace_existing=True,
                    )
                case PeriodUnit.WEEKS:
                    return self.scheduler.add_job(
                        id=id,
                        func=func,
                        args=args,
                        trigger=trigger,
                        weeks=period_value,
                        max_instances=1,
                        misfire_grace_time=30,
                        coalesce=True,
                        replace_existing=True,
                    )
                case _:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid period unit for job {id}",
                    )

    def schedule_cron_job(self, auth: Auth, body: CreateJobMetadataBody) -> Job | None:
        match body.type:
            case JobType.UPLOADED_DOCUMENTS_SUMMARY:
                return self.schedule_uploaded_documents_summary_agent(auth, body)
            case _:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid job type {body.type}",
                )

    def schedule_uploaded_documents_summary_agent(
        self, auth: Auth, body: CreateJobMetadataBody
    ) -> Job | None:
        if body.period_unit not in VALID_PERIODS_PER_JOB_TYPE.get(body.type, []):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid period unit for job type {body.type}",
            )

        if body.period_value <= 0:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid period value for job type {body.type}",
            )

        if not body.metadata:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid metadata for job type {body.type}",
            )

        document_types = body.metadata.get("document_types")
        if not document_types:
            raise HTTPException(
                status_code=400,
                detail=f"Input document types are required for job type {body.type}",
            )

        if not isinstance(document_types, list) or not all(
            document_type in DOCUMENT_TYPES for document_type in document_types
        ):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid document types for job type {body.type}",
            )

        days_back = body.metadata.get("days_back")
        if not days_back:
            raise HTTPException(
                status_code=400,
                detail=f"Input days back is required for job type {body.type}",
            )

        if isinstance(days_back, str):
            if days_back.isdigit():
                days_back = int(days_back)
            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid days back for job type {body.type}",
                )

        if days_back < 1 or days_back > 31:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid days back for job type {body.type}",
            )

        return self.add_cron_job(
            id=f"uploaded_documents_summary_{datetime.now().isoformat()}",
            func=run_uploaded_document_summary,
            args=[
                UploadedDocumentSummaryInput(
                    project_id=auth.project.id,
                    document_types=document_types,
                    days_back=days_back,
                    user_email=auth.user.clerk.email,
                    name=body.name,
                )
            ],
            period_unit=body.period_unit,
            period_value=body.period_value,
        )

    async def post_one(
        self,
        auth: Auth,
        body: CreateJobMetadataBody,
    ) -> JobMetadataResponse:
        job = self.schedule_cron_job(auth, body)
        if not job:
            raise HTTPException(status_code=500, detail="Error creating job")
        input_data = InsertJobMetadataInput(
            job_id=job.id,
            project_id=auth.project.id,
            user_id=auth.user.id,
            name=body.name,
            type=body.type,
            period_unit=body.period_unit,
            period_value=body.period_value,
            metadata=body.metadata,
        )
        job_metadata = await self.job_metadata_repository.insert_one(input_data)
        if not job_metadata:
            raise HTTPException(status_code=500, detail="Error creating job metadata")
        return self._to_job_metadata_response(job_metadata)

    async def get_one(
        self,
        auth: Auth,
        job_metadata_id: str,
    ) -> JobMetadataResponse | None:
        job_metadata = await self.job_metadata_repository.find_one(
            job_metadata_id=job_metadata_id,
            project_id=auth.project.id,
            user_id=auth.user.id,
        )
        if not job_metadata:
            raise HTTPException(status_code=404, detail="Job metadata not found")
        return self._to_job_metadata_response(job_metadata)

    async def get_many(
        self,
        auth: Auth,
        type: JobType | None = None,
    ) -> list[JobMetadataResponse]:
        job_metadatas = await self.job_metadata_repository.find_many(
            project_id=auth.project.id,
            user_id=auth.user.id,
            type=type,
        )
        return [
            self._to_job_metadata_response(job_metadata)
            for job_metadata in job_metadatas
        ]

    async def paginate(
        self,
        auth: Auth,
        type: JobType | None = None,
        name: str | None = None,
        page_size: int = 10,
        page_number: int = 1,
    ) -> PaginationResponse[JobMetadataResponse]:
        result = await self.job_metadata_repository.paginate(
            project_id=auth.project.id,
            user_id=auth.user.id,
            type=type,
            name=name,
            page_size=page_size,
            page_number=page_number,
        )

        if not result:
            return PaginationResponse(data=[], pagination=result.pagination)

        job_metadata_responses = [
            self._to_job_metadata_response(job_metadata) for job_metadata in result.data
        ]

        return PaginationResponse(
            data=job_metadata_responses,
            pagination=result.pagination,
        )

    async def update_one(
        self,
        auth: Auth,
        job_metadata_id: str,
        body: UpdateJobMetadataBody,
    ) -> JobMetadataResponse | None:
        existing_job_metadata = await self.job_metadata_repository.find_one(
            job_metadata_id=job_metadata_id,
            project_id=auth.project.id,
            user_id=auth.user.id,
        )

        if not existing_job_metadata:
            raise HTTPException(status_code=404, detail="Job metadata not found")

        new_job = None

        if (
            body.period_unit or body.period_value or body.metadata
        ) and existing_job_metadata.job_id:
            try:
                self.scheduler.remove_job(existing_job_metadata.job_id)
            except Exception as e:
                self.logger.warning(f"Error removing job: {e}")

            if existing_job_metadata.type == JobType.UPLOADED_DOCUMENTS_SUMMARY:
                updated_metadata = existing_job_metadata.metadata
                if body.metadata:
                    updated_metadata = body.metadata

                document_types = updated_metadata.get("document_types")
                if not document_types or not isinstance(document_types, list):
                    raise HTTPException(
                        status_code=400,
                        detail=f"Document types are required for job type {existing_job_metadata.type}",
                    )

                days_back = updated_metadata.get("days_back")
                if not days_back:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Days back is required for job type {existing_job_metadata.type}",
                    )

                if isinstance(days_back, str):
                    if days_back.isdigit():
                        days_back = int(days_back)
                    else:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Invalid days back for job type {existing_job_metadata.type}",
                        )

                if days_back < 1 or days_back > 31:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Days back must be between 1 and 31 for job type {existing_job_metadata.type}",
                    )

                input_args = [
                    UploadedDocumentSummaryInput(
                        project_id=auth.project.id,
                        document_types=document_types,
                        days_back=days_back,
                        user_email=auth.user.clerk.email,
                        name=existing_job_metadata.name,
                    )
                ]

                new_job = self.add_cron_job(
                    id=f"uploaded_documents_summary_{datetime.now().isoformat()}",
                    func=run_uploaded_document_summary,
                    args=input_args,
                    period_unit=body.period_unit or existing_job_metadata.period_unit,
                    period_value=body.period_value
                    or existing_job_metadata.period_value,
                )

        if not new_job:
            raise HTTPException(status_code=500, detail="Error creating job")

        job_metadata = await self.job_metadata_repository.update_one(
            job_metadata_id=job_metadata_id,
            job_id=new_job.id,
            name=body.name,
            period_unit=body.period_unit,
            period_value=body.period_value,
            metadata=body.metadata,
        )

        if not job_metadata:
            raise HTTPException(status_code=500, detail="Error updating job metadata")

        return self._to_job_metadata_response(job_metadata)

    async def delete_one(
        self,
        auth: Auth,
        job_metadata_id: str,
    ) -> JobMetadataResponse | None:
        job_metadata = await self.job_metadata_repository.delete_one(
            job_metadata_id=job_metadata_id,
            project_id=auth.project.id,
            user_id=auth.user.id,
        )
        if not job_metadata:
            raise HTTPException(status_code=404, detail="Job metadata not found")
        try:
            if job_metadata.job_id:
                self.scheduler.remove_job(job_metadata.job_id)
        except Exception as e:
            self.logger.error("Error removing job", e)
        return self._to_job_metadata_response(job_metadata)

    async def delete_many(
        self,
        auth: Auth,
        body: DeleteJobMetadataBody,
    ) -> DeleteResponse:
        if not body.job_metadata_ids:
            raise HTTPException(status_code=400, detail="Job metadata IDs are required")
        job_metadatas = await self.job_metadata_repository.delete_many(
            job_metadata_ids=body.job_metadata_ids,
            project_id=auth.project.id,
            user_id=auth.user.id,
        )
        for job_metadata in job_metadatas:
            try:
                if job_metadata.job_id:
                    self.scheduler.remove_job(job_metadata.job_id)
            except Exception as e:
                self.logger.error("Error removing job", e)
        return DeleteResponse(
            count=len(job_metadatas),
            ids=[item.id for item in job_metadatas],
        )
