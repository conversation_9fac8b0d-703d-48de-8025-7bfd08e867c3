from datetime import datetime

from bson import ObjectId
from pytz import utc

from src.models.shared import ConfigModel


class Prompt(ConfigModel):
    id: str
    project_id: str
    user_id: str
    name: str
    text: str
    created_at: datetime = datetime.now(utc)


class PostPromptBody(ConfigModel):
    name: str
    text: str


class PatchPromptBody(ConfigModel):
    name: str
    text: str


class InsertPromptInput(ConfigModel):
    project_id: str
    user_id: str
    name: str
    text: str

    def to_dict(self) -> dict:
        return {
            "project_id": ObjectId(self.project_id),
            "user_id": ObjectId(self.user_id),
            "name": self.name,
            "text": self.text,
            "created_at": datetime.now(utc),
        }


class UpdatePromptInput(ConfigModel):
    name: str
    text: str


class DeletePromptsBody(ConfigModel):
    prompt_ids: list[str] | None = None


class PromptResponse(ConfigModel):
    id: str
    user_id: str
    name: str
    text: str
    created_at: datetime
