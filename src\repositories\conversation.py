from datetime import datetime, timedelta

from bson import ObjectId
from pymongo import DESCENDING
from pytz import utc

from src.models.message import UpdateMessageInput
from src.models.conversation import (
    DEFAULT_CONVERSATION_TITLE,
    Conversation,
    ConversationUsageResponse,
    DocumentScore,
    InsertConversationInput,
    UpdateConversationInput,
)
from src.repositories.message import MessageRepository
from src.util.mongo import db


class ConversationRepository:
    def __init__(self):
        self.collection = db.get_collection("conversations")
        self.message_repository = MessageRepository()

    def _to_model(self, conversation_dict: dict) -> Conversation:
        conversation_dict["messages"].sort(
            key=lambda x: x["created_at"].replace(tzinfo=utc)
        )
        user_id = conversation_dict.get("user_id")
        if user_id:
            user_id = str(user_id)
        return Conversation(
            id=str(conversation_dict.get("_id")),
            project_id=str(conversation_dict.get("project_id")),
            user_id=user_id,
            title=conversation_dict.get("title") or DEFAULT_CONVERSATION_TITLE,
            messages=[
                self.message_repository._to_model(message)
                for message in conversation_dict.get("messages")
            ],
            scores=[
                self._to_score_model(score) for score in conversation_dict.get("scores")
            ],
            created_at=conversation_dict.get("created_at"),
        )

    def _to_score_model(self, score_dict: dict) -> DocumentScore:
        return DocumentScore(
            document_id=str(score_dict.get("document_id")),
            name=score_dict.get("name"),
            slug=score_dict.get("slug"),
            url=score_dict.get("url"),
            size=score_dict.get("size"),
            content_type=score_dict.get("content_type"),
            text=score_dict.get("text"),
            score=score_dict.get("score"),
            source_url=score_dict.get("source_url"),
        )

    async def find_one(
        self,
        conversation_id: str | None = None,
        project_id: str | None = None,
        user_id: str | None = None,
    ) -> Conversation | None:
        filter = {}
        if conversation_id:
            filter["_id"] = ObjectId(conversation_id)
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        conversation_dict = await self.collection.find_one(filter=filter)
        return self._to_model(conversation_dict) if conversation_dict else None

    async def find_many(
        self,
        conversation_ids: list[str] | None = None,
        project_id: str | None = None,
        user_id: str | None = None,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[Conversation]:
        filter = {}
        if conversation_ids:
            filter["_id"] = {
                "$in": [
                    ObjectId(conversation_id) for conversation_id in conversation_ids
                ]
            }
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        conversation_dicts = await self.collection.find(
            filter=filter, sort=sort
        ).to_list()
        return [
            self._to_model(conversation_dict)
            for conversation_dict in conversation_dicts
        ]

    async def insert_one(self, input: InsertConversationInput) -> Conversation | None:
        result = await self.collection.insert_one(input.to_dict())
        return await self.find_one(conversation_id=result.inserted_id)

    async def insert_many(
        self,
        inputs: list[InsertConversationInput],
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[Conversation]:
        conversation_dicts = [input.to_dict() for input in inputs]
        result = await self.collection.insert_many(conversation_dicts)
        return await self.find_many(
            conversation_ids=result.inserted_ids,
            sort=sort,
        )

    async def update_one(
        self,
        conversation_id: str,
        input: UpdateConversationInput,
        project_id: str | None = None,
        user_id: str | None = None,
    ) -> Conversation | None:
        conversation = await self.find_one(
            conversation_id=conversation_id,
            project_id=project_id,
            user_id=user_id,
        )
        if not conversation:
            return None

        update = {}
        if input.title:
            update["title"] = input.title
        if input.add_messages:
            old_messages = [message.to_dict() for message in conversation.messages]
            new_messages = [message.to_dict() for message in input.add_messages]
            messages = old_messages + new_messages
            messages.sort(key=lambda x: x["created_at"].replace(tzinfo=utc))
            update["messages"] = messages
        if input.add_scores:
            scores_dict = {
                score.document_id: score.to_dict() for score in conversation.scores
            }
            for score in input.add_scores:
                scores_dict[score.document_id] = score.to_dict()
            update["scores"] = list(scores_dict.values())

        await self.collection.update_one(
            {"_id": ObjectId(conversation.id)},
            {"$set": update},
        )
        return await self.find_one(conversation_id=conversation_id)

    async def delete_one(
        self,
        conversation_id: str | None = None,
        project_id: str | None = None,
        user_id: str | None = None,
    ) -> Conversation | None:
        conversation = await self.find_one(
            conversation_id=conversation_id,
            project_id=project_id,
            user_id=user_id,
        )
        if not conversation:
            return None
        await self.collection.delete_one(filter={"_id": ObjectId(conversation.id)})
        return conversation

    async def delete_many(
        self,
        conversation_ids: list[str] | None = None,
        project_id: str | None = None,
        user_id: str | None = None,
    ) -> list[Conversation]:
        conversations = await self.find_many(
            conversation_ids=conversation_ids,
            project_id=project_id,
            user_id=user_id,
        )
        if not conversations:
            return []
        await self.collection.delete_many(
            filter={
                "_id": {
                    "$in": [ObjectId(conversation.id) for conversation in conversations]
                }
            }
        )
        return conversations

    async def delete_empty(self, user_id: str) -> list[Conversation]:
        conversations = await self.find_many(user_id=user_id)
        if not conversations:
            return []
        conversation_ids = [
            conversation.id
            for conversation in conversations
            if not conversation.messages
        ]
        await self.collection.delete_many(
            filter={
                "_id": {
                    "$in": [
                        ObjectId(conversation_id)
                        for conversation_id in conversation_ids
                    ]
                }
            }
        )
        return conversations

    async def delete_document_scores(self, document_ids: list[str]):
        object_ids = [ObjectId(doc_id) for doc_id in document_ids]
        await self.collection.update_many(
            {"scores.document_id": {"$in": object_ids}},
            {"$pull": {"scores": {"document_id": {"$in": object_ids}}}},
        )

    async def usage(self, project_id: str, user_id: str) -> ConversationUsageResponse:
        filter = {"project_id": ObjectId(project_id)}
        if user_id:
            filter["user_id"] = ObjectId(user_id)

        result = await self.collection.aggregate(
            [
                {"$match": filter},
                {
                    "$facet": {
                        "total_count": [{"$count": "count"}],
                        "per_day": [
                            {
                                "$match": {
                                    "created_at": {
                                        "$gte": datetime.now() - timedelta(days=30)
                                    }
                                }
                            },
                            {
                                "$group": {
                                    "_id": {
                                        "$dateToString": {
                                            "format": "%Y-%m-%d",
                                            "date": "$created_at",
                                        }
                                    },
                                    "count": {"$sum": 1},
                                }
                            },
                            {"$sort": {"_id": -1}},
                            {"$project": {"date": "$_id", "count": 1, "_id": 0}},
                        ],
                    }
                },
            ]
        ).to_list()

        if not result or not result[0]["total_count"]:
            return ConversationUsageResponse(total_count=0, per_day=[])

        return ConversationUsageResponse(
            total_count=result[0]["total_count"][0]["count"],
            per_day=result[0]["per_day"],
        )

    async def update_conversation_message(
        self, conversation_id: str, message_id: str, input: UpdateMessageInput
    ) -> Conversation | None:
        conversation = await self.find_one(conversation_id=conversation_id)
        if not conversation:
            return None
        for message in conversation.messages:
            if message.id == message_id:
                message.context_id = input.context_id
                break
        return await self.collection.update_one(
            {"_id": ObjectId(conversation_id)},
            {"$set": {"messages": [message.to_dict() for message in conversation.messages]}},
        )
