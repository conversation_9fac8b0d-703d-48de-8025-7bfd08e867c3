from datetime import datetime

from pytz import utc

from src.models.clerk import <PERSON><PERSON><PERSON>, ClerkUserMembership
from src.models.shared import ConfigModel


class User(ConfigModel):
    id: str
    clerk_user_id: str
    clerk: Clerk<PERSON>ser | None = None
    memberships: list[ClerkUserMembership] = []
    accepted_terms: bool = False
    created_at: datetime = datetime.now(utc)


class UserResponse(ConfigModel):
    id: str
    clerk_user_id: str
    organization_id: str
    clerk_organization_id: str
    accepted_terms: bool
    created_at: datetime


class PatchUserBody(ConfigModel):
    accepted_terms: bool | None = None


class InsertUserInput(ConfigModel):
    clerk_user_id: str
    accepted_terms: bool = False

    def to_dict(self) -> dict:
        return {
            "clerk_user_id": self.clerk_user_id,
            "accepted_terms": self.accepted_terms,
            "created_at": datetime.now(utc),
        }


class UpdateUserInput(ConfigModel):
    accepted_terms: bool | None = None
