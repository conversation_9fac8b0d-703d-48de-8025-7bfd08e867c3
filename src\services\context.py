import asyncio
from datetime import datetime

from fastapi import HTTPException
from pymongo import ASCENDING

from src.models.auth import Auth
from src.models.context import (
    Context,
    ContextResponse,
    DeleteContextsBody,
    InsertContextInput,
    PostContextsBody,
)
from src.models.message import UpdateMessageInput
from src.models.shared import DeleteResponse, PaginationResponse
from src.models.task import InsertTaskInput, TaskStatus
from src.repositories.context import ContextRepository
from src.repositories.conversation import ConversationRepository
from src.repositories.document import DocumentRepository
from src.repositories.message import MessageRepository
from src.repositories.task import TaskRepository
from src.services.pinecone import PineconeService


class ContextService:
    def __init__(self):
        self.context_repository = ContextRepository()
        self.task_repository = TaskRepository()
        self.pinecone_service = PineconeService()
        self.document_repository = DocumentRepository()
        self.conversation_repository = ConversationRepository()

    async def create(
        self,
        auth: Auth,
        text: str,
        conversation_id: str | None = None,
        user_id: str | None = None,
        external_id: str | None = None,
        document_id: str | None = None,
        message_id: str | None = None,
    ) -> Context:
        context = await self.context_repository.insert_one(
            input=InsertContextInput(
                project_id=auth.project.id,
                text=text,
                conversation_id=conversation_id,
                user_id=auth.get_user_id(user_id),
                external_id=external_id,
                document_id=document_id,
                message_id=message_id,
            )
        )

        if not context:
            raise HTTPException(status_code=500, detail="Error creating context")

        if message_id:
            asyncio.create_task(
                self.conversation_repository.update_conversation_message(
                    conversation_id=conversation_id,
                    message_id=message_id,
                    input=UpdateMessageInput(context_id=context.id),
                )
            )

        asyncio.create_task(
            self.task_repository.insert_one(
                input=InsertTaskInput(context_id=context.id)
            )
        )

        return context

    async def post_many(
        self,
        auth: Auth,
        body: PostContextsBody,
    ) -> list[ContextResponse]:
        context_inputs = []
        for context in body.contexts:
            context_input = InsertContextInput(
                project_id=auth.project.id,
                text=context.text,
                conversation_id=context.conversation_id,
                document_id=context.document_id,
                user_id=auth.get_user_id(),
                external_id=context.external_id,
            )
            context_inputs.append(context_input)
        contexts = await self.context_repository.insert_many(context_inputs)

        task_inputs = []
        for context in contexts:
            task_input = InsertTaskInput(context_id=context.id)
            task_inputs.append(task_input)
        asyncio.create_task(self.task_repository.insert_many(task_inputs))

        return [self._to_context_response(context) for context in contexts]

    async def get_one(
        self,
        auth: Auth,
        context_id: str,
    ) -> ContextResponse:
        (context, task) = await asyncio.gather(
            self.context_repository.find_one(
                project_id=auth.project.id,
                context_id=context_id,
            ),
            self.task_repository.find_last_one(context_id=context_id),
        )
        if not context:
            raise HTTPException(status_code=404, detail="Context not found")
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        return self._to_context_response(context=context, status=task.status)

    async def get_many(
        self,
        auth: Auth,
        context_ids: list[str] | None = None,
        external_ids: list[str] | None = None,
        user_id: str | None = None,
        conversation_id: str | None = None,
    ) -> list[ContextResponse]:
        contexts = await self.context_repository.find_many(
            project_id=auth.project.id,
            context_ids=context_ids,
            external_ids=external_ids,
            user_id=user_id,
            conversation_id=conversation_id,
        )

        if not contexts:
            return []

        tasks = await self.task_repository.find_many(
            context_ids=[context.id for context in contexts],
            sort=[("created_at", ASCENDING)],
        )
        task_statuses = {task.context_id: task.status for task in tasks}

        return [
            self._to_context_response(context, status=task_statuses.get(context.id))
            for context in contexts
        ]

    async def paginate(
        self,
        auth: Auth,
        context_ids: list[str] = [],
        external_ids: list[str] = [],
        user_id: str | None = None,
        conversation_id: str | None = None,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        page_size: int = 10,
        page_number: int = 1,
    ) -> PaginationResponse[ContextResponse]:
        result = await self.context_repository.paginate(
            project_id=auth.project.id,
            context_ids=context_ids,
            external_ids=external_ids,
            user_id=user_id,
            conversation_id=conversation_id,
            start_date=start_date,
            end_date=end_date,
            page_size=page_size,
            page_number=page_number,
        )

        if not result.data:
            return PaginationResponse(data=[], pagination=result.pagination)

        tasks = await self.task_repository.find_many(
            context_ids=[context.id for context in result.data],
            sort=[("created_at", ASCENDING)],
        )
        task_statuses = {task.context_id: task.status for task in tasks}

        context_responses = [
            self._to_context_response(context, status=task_statuses.get(context.id))
            for context in result.data
        ]
        return PaginationResponse(data=context_responses, pagination=result.pagination)

    async def delete_many(
        self,
        auth: Auth,
        body: DeleteContextsBody,
    ) -> DeleteResponse:
        if not auth.has_permission(body.user_id):
            raise HTTPException(
                status_code=403,
                detail="You can't delete another user's context",
            )

        contexts = await self.context_repository.delete_many(
            project_id=auth.project.id,
            context_ids=body.context_ids,
            external_ids=body.external_ids,
            document_ids=body.document_ids,
            user_id=body.user_id,
            conversation_id=body.conversation_id,
        )

        if not contexts:
            return DeleteResponse(count=0, ids=[])

        asyncio.gather(
            self.task_repository.delete_many(
                context_ids=[context.id for context in contexts],
            ),
            self.pinecone_service.delete(
                organization_id=auth.organization.id,
                project_id=auth.project.id,
                context_ids=[context.id for context in contexts],
            ),
        )

        return DeleteResponse(
            count=len(contexts),
            ids=[context.id for context in contexts],
        )

    def _to_context_response(
        self,
        context: Context,
        status: TaskStatus | None = None,
    ) -> ContextResponse:
        return ContextResponse(
            id=context.id,
            text=context.text,
            conversation_id=context.conversation_id,
            user_id=context.user_id,
            external_id=context.external_id,
            document_id=context.document_id,
            created_at=context.created_at,
            status=status or TaskStatus.PENDING,
        )
