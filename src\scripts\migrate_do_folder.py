import asyncio
import os
import aioboto3
import botocore

ORIGIN_BUCKET = "snapz-documents-dev"
DESTINATION_BUCKET = "query-engine-documents-dev"

session = aioboto3.Session()


async def upload_file(
    bucket: str, content: bytes, path: str, content_type: str = "application/pdf"
) -> bool:
    try:
        async with session.client(
            service_name="s3",
            region_name="sfo2",
            endpoint_url="https://sfo2.digitaloceanspaces.com",
            config=botocore.config.Config(s3={"addressing_style": "virtual"}),
            aws_access_key_id=os.getenv("DO_SPACES_ACCESS_KEY"),
            aws_secret_access_key=os.getenv("DO_SPACES_SECRET_KEY"),
        ) as client:
            await client.put_object(
                Bucket=bucket,
                Key=path,
                Body=content,
                ACL="public-read",
                ContentType=content_type,
            )
            return True
    except Exception as e:
        print(e)
        return False


async def download_file(bucket: str, path: str) -> bytes | None:
    try:
        async with session.client(
            service_name="s3",
            region_name="sfo2",
            endpoint_url="https://sfo2.digitaloceanspaces.com",
            config=botocore.config.Config(s3={"addressing_style": "virtual"}),
            aws_access_key_id=os.getenv("DO_SPACES_ACCESS_KEY"),
            aws_secret_access_key=os.getenv("DO_SPACES_SECRET_KEY"),
        ) as client:
            file = await client.get_object(Bucket=bucket, Key=path)
            return await file["Body"].read()
    except Exception as e:
        print(e)
        return None


async def list_files(bucket: str) -> list[str]:
    try:
        async with session.client(
            service_name="s3",
            region_name="sfo2",
            endpoint_url="https://sfo2.digitaloceanspaces.com",
            config=botocore.config.Config(s3={"addressing_style": "virtual"}),
            aws_access_key_id=os.getenv("DO_SPACES_ACCESS_KEY"),
            aws_secret_access_key=os.getenv("DO_SPACES_SECRET_KEY"),
        ) as client:
            response = await client.list_objects_v2(Bucket=bucket)
            return [obj["Key"] for obj in response.get("Contents", [])]
    except Exception as e:
        print(e)
        return []


async def migrate_do_folder():
    (origin_paths, destination_paths) = await asyncio.gather(
        *[list_files(ORIGIN_BUCKET), list_files(DESTINATION_BUCKET)]
    )

    paths_to_migrate = []
    for path in origin_paths:
        if path not in destination_paths and path.startswith("files/"):
            paths_to_migrate.append(path)

    async def migrate(path):
        content = await download_file(ORIGIN_BUCKET, path)
        if content:
            await upload_file(DESTINATION_BUCKET, content, path)
            print(f"Uploaded file: {path}")

    await asyncio.gather(*[migrate(path) for path in paths_to_migrate]) 

asyncio.run(migrate_do_folder())
