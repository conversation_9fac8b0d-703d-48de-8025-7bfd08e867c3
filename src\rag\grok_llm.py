import os
from typing import Any, Optional

from llama_index.core.llms import (
    CompletionResponse,
    CompletionResponseGen,
    CustomLLM,
    LLMMetadata,
)
from llama_index.core.llms.callbacks import llm_completion_callback
from openai import OpenAI as OpenAIClient
from pydantic import Field


class Grok(CustomLLM):
    """
    Custom LLM implementation for the Grok API, structured in the demo style.

    This version follows the exact structure of the demo OurLLM class while
    preserving the functionality of the original Grok.
    """

    model_config = {"protected_namespaces": ()}

    # Define class variables for Pydantic model
    context_window: int = Field(default=32768)
    num_output: int = Field(default=4096)
    model_name: str = Field(default="grok-3-mini")
    api_key: Optional[str] = Field(default=None)
    temperature: float = Field(default=0.7)
    client: Optional[OpenAIClient] = Field(default=None, exclude=True)

    def __init__(
        self,
        api_key: Optional[str] = None,
        model_name: str = "grok-3-mini",
        temperature: float = 0.7,
        num_output: int = 4096,
        **kwargs: Any,
    ) -> None:
        """
        Initialize the Grok.

        Args:
            api_key: xAI API key (will use GROK_API_KEY env var if not provided)
            model_name: The Grok model to use
            temperature: Sampling temperature
            num_output: Maximum number of tokens to generate
            **kwargs: Additional arguments
        """
        kwargs.update(
            {
                "api_key": api_key or os.environ.get("GROK_API_KEY"),
                "model_name": model_name,
                "temperature": temperature,
                "num_output": num_output,
            }
        )

        super().__init__(**kwargs)

        if not self.api_key:
            raise ValueError(
                "Please provide an xAI API key via the api_key parameter or "
                "the GROK_API_KEY environment variable."
            )

        self.context_window = 32768 if "beta" in self.model_name else 16384
        self.client = OpenAIClient(
            api_key=self.api_key,
            base_url="https://api.x.ai/v1",
        )

    @property
    def metadata(self) -> LLMMetadata:
        """Get LLM metadata."""
        return LLMMetadata(
            context_window=self.context_window,
            num_output=self.num_output,
            model_name=self.model_name,
            is_chat_model=True,
        )

    @llm_completion_callback()
    def complete(self, prompt: str, **kwargs: Any) -> CompletionResponse:
        """
        Complete the prompt using the Grok model.

        Args:
            prompt: The prompt to complete
            **kwargs: Additional arguments

        Returns:
            A CompletionResponse containing the completed text
        """
        if "formatted" in kwargs:
            kwargs.pop("formatted")

        completion = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": prompt}],
            temperature=self.temperature,
            max_tokens=self.num_output,
            **kwargs,
        )

        return CompletionResponse(
            text=completion.choices[0].message.content,
        )

    @llm_completion_callback()
    def stream_complete(self, prompt: str, **kwargs: Any) -> CompletionResponseGen:
        """
        Stream the prompt completion using the Grok model.

        Args:
            prompt: The prompt to complete
            **kwargs: Additional arguments

        Returns:
            A generator yielding tokens as they become available
        """
        if "formatted" in kwargs:
            kwargs.pop("formatted")

        completion_stream = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": prompt}],
            temperature=self.temperature,
            max_tokens=self.num_output,
            stream=True,
            **kwargs,
        )

        def gen() -> CompletionResponseGen:
            text = ""
            for chunk in completion_stream:
                if not chunk.choices:
                    continue
                content = chunk.choices[0].delta.content
                if content is not None:
                    text += content
                    yield CompletionResponse(text=text, delta=content)

        return gen()
