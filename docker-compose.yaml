services:
  mongodb:
    image: mongo
    command: bash -c "mongod --quiet --logpath /dev/null --bind_ip_all --replSet rs0 && mongo --eval 'rs.initiate()'"
    restart: always
    ports:
      - 27017:27017
    volumes:
      - ~/apps/mongo:/data/db
    networks:
      - local-network
  api:
    build:
      context: .
      dockerfile: build/api/Dockerfile
    ports:
      - 8080:8080
    env_file:
      - path: env/.env.docker
    depends_on:
      - mongodb
    networks:
      - local-network
  worker:
    build:
      context: .
      dockerfile: build/worker/Dockerfile
    env_file:
      - path: env/.env.docker
    depends_on:
      - mongodb
    networks:
      - local-network
networks:
  local-network: