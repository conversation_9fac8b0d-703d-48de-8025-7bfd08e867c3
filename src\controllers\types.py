from fastapi import APIRouter

from src.models.document import CONTENT_TYPES, DOCUMENT_CATEGORIES, DOCUMENT_TYPES
from src.models.llm import LLM_MODELS, LLM_PROVIDERS, PROVIDER_MODEL_DICT

types_router = APIRouter()


@types_router.get("/document-types", response_model=list[str])
async def get_document_types():
    return DOCUMENT_TYPES


@types_router.get("/document-categories", response_model=list[str])
async def get_document_categories():
    return DOCUMENT_CATEGORIES


@types_router.get("/content-types", response_model=list[str])
async def get_content_types():
    return CONTENT_TYPES


@types_router.get("/llm-providers", response_model=list[str])
async def get_llm_providers():
    return LLM_PROVIDERS


@types_router.get("/llm-models", response_model=list[str])
async def get_llm_models():
    return LLM_MODELS


@types_router.get("/provider-model", response_model=dict)
async def get_provider_model():
    return PROVIDER_MODEL_DICT
