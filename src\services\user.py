from src.models.auth import Auth
from src.models.user import PatchUserBody, UpdateUserInput, User, UserResponse
from src.repositories.user import UserRepository
from src.util.logger import Logger
from src.util.mongo import db


class UserService:
    def __init__(self):
        self.collection = db.get_collection("users")
        self.logger = Logger("UserService")
        self.user_repository = UserRepository()

    def find_current_user(self, auth: Auth) -> UserResponse:
        return self._to_user_response(auth)

    async def patch_one(
        self,
        auth: Auth,
        body: PatchUserBody,
    ) -> UserResponse | None:
        user = await self.user_repository.update_one(
            user_id=auth.user.id,
            input=UpdateUserInput(accepted_terms=body.accepted_terms),
        )
        return self._to_user_response(auth, user) if user else None

    def _to_user_response(
        self,
        auth: Auth,
        user: User | None = None,
    ) -> UserResponse:
        if not user:
            user = auth.user
        return UserResponse(
            id=user.id,
            clerk_user_id=user.clerk_user_id,
            organization_id=auth.organization.id,
            clerk_organization_id=auth.organization.clerk_organization_id,
            accepted_terms=user.accepted_terms,
            created_at=user.created_at,
        )
