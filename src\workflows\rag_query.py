import asyncio
import json
from datetime import datetime

from llama_index.core.chat_engine import CondensePlusContextChatEngine
from llama_index.core.indices.query.query_transform import DecomposeQueryTransform
from llama_index.core.llms import ChatMessage
from llama_index.core.node_parser import SimpleNodeParser
from llama_index.core.postprocessor import SimilarityPostprocessor
from llama_index.core.prompts.base import PromptTemplate
from llama_index.core.prompts.prompt_type import PromptType
from llama_index.core.workflow import (
    Context,
    Event,
    StartEvent,
    StopEvent,
    Workflow,
    step,
)
from llama_index.llms.anthropic import Anthropic
from llama_index.llms.gemini import Gemini
from llama_index.llms.groq import Groq
from llama_index.llms.openai import OpenAI
from pytz import utc

from src.models.document import DOCUMENT_CATEGORIES, DOCUMENT_TYPES
from src.models.auth import UserData
from src.models.llama_index import RetrieverFilter
from src.models.rag_query import (
    Date<PERSON><PERSON><PERSON>,
    DocumentCategory,
    DocumentType,
    RAGSearchKeywords,
    SearchType,
)
from src.models.shared import ConfigModel
from src.rag.config import GEMINI_LLM
from src.rag.grok_llm import Grok
from src.rag.postprocessor import (
    EmptyResponsePostprocessor,
    FullDocumentPostprocessor,
    RerankPostprocessor,
    ScoreNormalizerPostprocessor,
)
from src.repositories.document import DocumentRepository
from src.services.retriever import RetrieverService
from src.util.logger import Logger


class RAGQueryInput(ConfigModel):
    query: str
    organization_id: str
    project_id: str
    selected_document_ids: list[str]
    user_data: UserData
    filter: RetrieverFilter
    chat_history: list[ChatMessage]
    answer_llm: OpenAI | Gemini | Groq | Anthropic | Grok
    web_search: bool = False


class QueryExpansionEvent(Event):
    input: RAGQueryInput


class SearchTypeEvent(Event):
    input: RAGQueryInput


class SemanticSearchEvent(Event):
    input: RAGQueryInput


class KeywordDetectionEvent(Event):
    input: RAGQueryInput


class KeywordSearchEvent(Event):
    input: RAGQueryInput


class WebSearchEvent(Event):
    input: RAGQueryInput


class StreamQueryEvent(Event):
    input: RAGQueryInput


SEARCH_TYPE_TEMPLATE = """
    You are a helpful AI assistant that helps users find documents and perform actions on them.
    A user is going to provide you with a query.
    The user might have uploaded documents and might be asking a question based on the documents.
    Your job is to detect the type of search you need to perform based on the user query.
    These are the types of search available to you:

    1. Semantic search: (semantic_search) 
    The user query is a question that can be answered by using semantic search.
    The answer is related to or can be found in the documents, a knowledge base must be used to answer the question.
    Examples: 
    - "Who is the author of document X?"
    - "When was the contract signed for the project X?"
    - "What is the name of the person who signed the contract?"
    - "What are the key risks associated with Project Y?"

    2. Keyword search: (keyword_search) 
    The user query is a request to find documents that contain specific keywords or an action to be performed on the documents.
    An action is performed on the documents that contain the keywords.
    Examples: 
    - "Find and sum all my invoices"
    - "Make a summary of all my contracts"
    - "Create a table with the key details of my submittals"
    - "List all contracts related to 'Project X'."

    Edge Cases and Nuances:
    - If the query mixes question-answering and keyword-based actions, lean towards "keyword_search". For example, "Find invoices from John Smith and summarize them" should be "keyword_search".
    - If the query contains a document ID, prioritize "keyword_search" to locate the specific document. For example, "What is the status of document ABC-123?" should be "keyword_search".
    - If the user explicitly asks to "find" something, prioritize "keyword_search".
    - If the query is vague or lacks specific keywords, and the intent is unclear, default to "semantic_search". For example, "Tell me about my projects" is vague.
    - If you are not confident or don't know the answer, return the "semantic_search" type.

    Output Format: Provide the search type (either "semantic_search" or "keyword_search").
    User query: {query}
    Search type:
"""

SEARCH_TYPE_PROMPT = PromptTemplate(
    template=SEARCH_TYPE_TEMPLATE,
    prompt_type=PromptType.SINGLE_SELECT,
)


DECOMPOSE_QUERY_TRANSFORM_TEMPLATE = """
    Original user question or query: {query_str}
    Knowledge source context: {context_str} 
    We have an opportunity to turn the user query from a question into a clear and concise instruction, if it applies.
    The instruction has to be an action to be performed on the documents.
    Here is a description of possible actions that can be performed on the documents:
    - FIND: Search for documents that match the user query or contain specific information.
    - SUM: Calculate the sum of values extracted from relevant documents (e.g., invoice amounts).
    - SUMMARIZE: Create a concise summary of the key information found across multiple documents.
    - CREATE TABLE: Extract specific fields from the document and create a table with the extracted information.
    - EXTRACT: Get specific info from the documents, such as a name, date or number.

    Examples:
    User query: "How many documents did I upload last year?" -> "Find all my documents uploaded last year"
    User query: "Do I have any documents about healthcare?" -> "Find all my documents about healthcare"
    User query: "Did I upload any invoices?" -> "Find all my invoices"
    User query: "Who is the author of document X?" -> "Find who is the author of document X"
    User query: "When was the contract signed for the project X?" -> "Find when was the contract signed for the project X"
    User query: "What is the name of the person who signed the contract?" -> "Find the name of the person who signed the contract"
    User query: "Sum all my invoices from January" -> "Find and calculate the sum of all my invoices from January"
    
    If the user query is already an instruction, return it as is.
    Do not remove any details or add any extra details from the user query.
    If the user query is an open ended question, return it as is.
    Generate the new instruction or return the original instruction:
"""

DECOMPOSE_QUERY_TRANSFORM_PROMPT = PromptTemplate(
    template=DECOMPOSE_QUERY_TRANSFORM_TEMPLATE,
    prompt_type=PromptType.DECOMPOSE,
)

SEMANTIC_SEARCH_TEMPLATE = """
    # Role
    You are an advanced AI assistant designed to provide helpful, accurate, and contextually relevant responses to user queries. 
    Always aim to be informative, concise, and user-friendly.

    # Context
    You have access to a knowledgebase that contains information about the user's documents and projects.
    You have access to the user's data and the documents they have uploaded.
    User data: {user_data}

    # Task
    Your task is to answer the user's query based on the information available in the knowledgebase.
    You will be given a query and you will need to answer it based on the information available.
    Provide context or background when helpful, and cite knowledgebase sources for accuracy.
    At the end of your response, offer two related, thought-provoking follow up questions to help the user explore the topic further.

    # Output
    Be clear, concise, and directly address the query.
    Use a professional, approachable tone.
    If the user asks for a table, they want you to return your response in a markdown table without extra spaces.
    For the follow up questions, write each question inside <follow_up></follow_up> tags.

    # Constraints
    Avoid speculation or unsupported information.
    Avoid showing the user any sql code or executable code in any programming language unless the user asks for it.
"""

SEMANTIC_SEARCH_PROMPT = PromptTemplate(
    template=SEMANTIC_SEARCH_TEMPLATE,
    prompt_type=PromptType.QUESTION_ANSWER,
)

DOCUMENT_TYPE_DETECTION_TEMPLATE = """
    You are a helpful AI assistant.
    A user is going to provide you with a query.
    Your job is to find different document type keywords in the user query.
    You have a list of document types available to you.
    You have to identify up to 3 document types from the list.
    You cannot invent new document types that are not in the list.
    It is not mandatory to extract document types from the user query.
    Do not include any markdown formatting in the result.
    Only select a document type if:
    - It is explicitly mentioned in the user query
    - It is very similar or a synonym to one of the document types in the list

    These are the document types list available to you:
    {document_types}
    The user query is: {query}

    Examples:
    - "Find all my construction submittals and invoices" -> ["submittal", "invoice_receipt"]
    - "Find all my invoices about healthcare" -> ["invoice_receipt"]
    - "Show me the contract details" -> ["contract"]
    - "Get me the purchase orders from last quarter" -> ["purchase_order"]
    - "List all expense reports" -> ["expense_report"]

    Negative Examples (What NOT to Extract):
    - "Find all documents related to construction projects" -> [] (Because "construction projects" isn't a specific document type)
    - "Summarize everything related to accounting" -> [] (Because "accounting" isn't a specific document type)
    - "Get me the latest updates" -> [] (Because "updates" isn't a document type)

    Now, return the document types that are in the user query in a json list with the following format:
    ["document_type_1", "document_type_2", "document_type_3"]
"""

DOCUMENT_TYPE_DETECTION_PROMPT = PromptTemplate(
    template=DOCUMENT_TYPE_DETECTION_TEMPLATE,
    prompt_type=PromptType.KEYWORD_EXTRACT,
)


DOCUMENT_CATEGORY_DETECTION_TEMPLATE = """
    You are a helpful AI assistant.
    A user is going to provide you with a query.
    Your job is to find different document category keywords in the user query.
    You have a list of document categories available to you.
    You have to identify up to 3 document categories from the list.
    You cannot invent new document categories that are not in the list.
    It is not mandatory to extract document categories from the user query.
    Only select a document category if it is explicitly mentioned in the user query.
    If the exact document category is not found in the query, return an empty list.
    Do not include any markdown formatting in the result.

    These are the document categories list available to you:
    {document_categories}
    The user query is: {query}

    Examples:
    - "Find all my construction submittals" -> ["real_estate"]
    - "Find all real estate contracts" -> ["real_estate"]
    - "Show me legal invoices" -> ["legal"]
    - "Get all healthcare related documents" -> ["healthcare"]
    - "All HR policies please" -> ["human_resources"]

    Negative Examples (What NOT to Extract):
    - "Sum all my invoices" -> [] (Because "sum" doesn't imply a document category)
    - "Find all documents from John Smith" -> [] (Because "John Smith" is a person, not a category)
    - "Show me all my recent files" -> [] (Because "recent" is a time frame, not a category)

    Now, return the document categories that are in the user query in a json list with the following format:
    ["document_category_1", "document_category_2", "document_category_3"]
"""

DOCUMENT_CATEGORY_DETECTION_PROMPT = PromptTemplate(
    template=DOCUMENT_CATEGORY_DETECTION_TEMPLATE,
    prompt_type=PromptType.KEYWORD_EXTRACT,
)

ENTITY_DETECTION_TEMPLATE = """
    You are a helpful AI assistant.
    A user is going to provide you with a query.
    Your job is to find different IDs in the user query.
    The IDs can be alphanumeric or numeric values.
    The IDs must be longer than 7 characters to be considered an ID, or be an external ID.
    The only exception to select and ID with less than 7 characters is if it is an external ID.
    You have to identify up to 3 different IDs from the query.
    You cannot invent new IDs that are not in the query.
    It is not mandatory to extract IDs from the user query.
    Only select an ID if it is explicitly mentioned in the user query.
    Be careful, do not confuse numbers that are actually dates with IDs.

    Specific Instructions:
    - Only extract strings that are likely to be document or project identifiers.
    - If multiple potential IDs are present, prioritize those that match a known ID format in your organization (e.g., "PROJ-2024-123", "INV-2023-001").
    - If you are not sure about extracting an ID, do not extract it.

    Examples:
    - "Find document PROJ-2024-123" -> ["PROJ-2024-123"]
    - "What is the status of INV-2023-001?" -> ["INV-2023-001"]
    - "Get me the details of contract ABCDEFG" -> ["ABCDEFG"]
    - "Project number 123456789" -> ["123456789"]
    - "External id Ext123" -> ["Ext123"]

    Negative Examples (What NOT to Extract):
    - "Find all documents from last week" -> [] (Because "last week" is a time frame, not an ID)
    - "Show me the latest version" -> [] (Because "latest version" is not a document id)
    - "Find all documents related to project alpha" -> [] (Because "alpha" is not a document id)
    - "What happened on 2024-01-15?" -> [] (Because "2024-01-15" is a date, not an ID)
    - "Find document 123" -> [] (Because "123" is shorter than 7 characters and its not an external id)
    
    The user query is: {query}
    
    Now, return the ids that are in the user query in a json list with the following format:
    ["id_1", "id_2", "id_3"]
"""

ENTITY_DETECTION_PROMPT = PromptTemplate(
    template=ENTITY_DETECTION_TEMPLATE,
    prompt_type=PromptType.KEYWORD_EXTRACT,
)

DATE_DETECTION_TEMPLATE = """
    You are a helpful AI assistant.
    A user is going to provide you with a query.
    The user has uploaded documents to a project.
    You have access to the uploaded documents and the date they were uploaded.
    Your job is to find and transform the uploaded date of a document in the user query to a range filter.
    You cannot invent dates that are not in the query.
    It is not mandatory to extract dates from the user query.
    If you are not confident or don't know the answer, return an empty json object.
    Do not include any markdown formatting in the result.

    Specific Instructions:
    - Interpret vague date references (e.g., "last week") relative to the *current date*, which is provided to you.
    - If the query contains an incomplete date (e.g., just a month or just a year), fill in the missing parts.
    - If you are not sure about extracting a date, do not extract it.

    (Let's assume the current date is 2024-02-01 only for these examples)
    Examples:
    - Find all my documents uploaded on 2024-01-01 -> {"start_date": "2024-01-01T00:00:00Z", "end_date": "2024-01-01T23:59:59Z"}
    - Find all my invoices uploaded from last month -> {"start_date": "2024-01-01T00:00:00Z", "end_date": "2024-01-31T23:59:59Z"}
    - How many documents did I upload last year? -> {"start_date": "2023-01-01T00:00:00Z", "end_date": "2023-12-31T23:59:59Z"}
    - Find documents uploaded from Q1 2023 -> {"start_date": "2023-01-01T00:00:00Z", "end_date": "2023-03-31T23:59:59Z"}
    - Show me documents uploaded from the past week -> {"start_date": "2024-01-25T00:00:00Z", "end_date": "2024-02-01T23:59:59Z"}
    - "Find all my files from 2023" -> {"start_date": "2023-01-01T00:00:00Z", "end_date": "2023-12-31T23:59:59Z"}
    - "Documents from January" -> {"start_date": "2024-01-01T00:00:00Z", "end_date": "2024-01-31T23:59:59Z"}

    Negative Examples (What NOT to Extract):
    - "Find all documents related to project budget" -> {} (Because "project budget" is not a date)
    - "Summarize the main points" -> {} (Because there's no date mentioned)
    - "Give me all files containing the number 12345" -> {} (Because there's no date mentioned)

    The user query is: {query}
    The current date is: {current_date}

    Now, return the date in a json with the following format:
    {"start_date": "2024-01-01T00:00:00Z", "end_date": "2024-01-01T23:59:59Z"}
"""

DATE_DETECTION_PROMPT = PromptTemplate(
    template=DATE_DETECTION_TEMPLATE,
    prompt_type=PromptType.KEYWORD_EXTRACT,
)

KEYWORD_SEARCH_TEMPLATE = """
    # Role
    You are an advanced AI assistant designed to provide helpful, accurate, and contextually relevant responses to user queries. 
    Always aim to be informative, concise, and user-friendly.

    # Context
    You have access to a knowledgebase that contains information about the user's documents and projects.
    You have access to the user's data and the documents they have uploaded.
    User data: {user_data}

    # Task
    Your task is to answer the user's query based on the information available in the knowledgebase.
    You will be given a query and you will need to answer it based on the information available.
    Provide context or background when helpful, and cite knowledgebase sources for accuracy.
    At the end of your response, offer two related, thought-provoking follow up questions to help the user explore the topic further.

    # Output
    Be clear, concise, and directly address the query.
    Use a professional, approachable tone.
    If the user asks for a table, they want you to return your response in a markdown table without extra spaces.
    For the follow up questions, write each question inside <follow_up></follow_up> tags.

    # Constraints
    Avoid speculation or unsupported information.
    Avoid showing the user any sql code or executable code in any programming language unless the user asks for it.
"""

KEYWORD_SEARCH_PROMPT = PromptTemplate(
    template=KEYWORD_SEARCH_TEMPLATE,
    prompt_type=PromptType.QUESTION_ANSWER,
)

WEB_SEARCH_TEMPLATE = """
    # Role
    You are an advanced AI assistant designed to provide helpful, accurate, and contextually relevant responses to user queries. 
    Always aim to be informative, concise, and user-friendly.

    # Context
    You have access to the internet and can search the web for information.
    You have access to the user's data.
    User data: {user_data}

    # Task
    Your task is to answer the user's query based on the information available in the web.
    You will be given a query and you will need to answer it based on the information available.
    Provide context or background when helpful, and cite web sources for accuracy.
    At the end of your response, offer two related, thought-provoking follow up questions to help the user explore the topic further.

    # Output
    Be clear, concise, and directly address the query.
    Use a professional, approachable tone.
    If the user asks for a table, they want you to return your response in a markdown table without extra spaces.
    For the follow up questions, write each question inside <follow_up></follow_up> tags.

    # Constraints
    Avoid speculation or unsupported information.
    Avoid showing the user any sql code or executable code in any programming language unless the user asks for it.
"""

WEB_SEARCH_PROMPT = PromptTemplate(
    template=WEB_SEARCH_TEMPLATE,
    prompt_type=PromptType.QUESTION_ANSWER,
)

DEFAULT_CONTEXT_PROMPT_TEMPLATE = """
  The following is a friendly conversation between a user and an AI assistant.
  The assistant is talkative and provides lots of specific details from its context.

  Here are the relevant documents for the context:
  {context_str}

  Instruction: Based on the above documents, provide a detailed answer for the user question below.
"""

DEFAULT_CONTEXT_REFINE_PROMPT_TEMPLATE = """
  The following is a friendly conversation between a user and an AI assistant.
  The assistant is talkative and provides lots of specific details from its context.

  Here are the relevant documents for the context:
  {context_msg}

  Existing Answer:
  {existing_answer}

  Instruction: 
  - Refine the existing answer using the provided context to assist the user.
  - If the context isn't helpful, just repeat the existing answer and nothing more.
"""

DEFAULT_CONDENSE_PROMPT_TEMPLATE = """
  Given the following conversation between a user and an AI assistant and a follow up question from user,
  rephrase the follow up question to be a standalone question.

  Chat History:
  {chat_history}
  
  Follow Up Input: {question}
  
  Standalone question:
"""


class RAGQueryWorkflow(Workflow):
    def __init__(self):
        super().__init__(timeout=180)
        self.retriever_service = RetrieverService()
        self.document_repository = DocumentRepository()
        self.node_parser = SimpleNodeParser()
        self.logger = Logger("RAGQueryWorkflow")

    @step
    async def query_expansion(self, ctx: Context, ev: StartEvent) -> SearchTypeEvent:
        decomposition_query_bundle = DecomposeQueryTransform(
            llm=GEMINI_LLM,
            decompose_query_prompt=DECOMPOSE_QUERY_TRANSFORM_PROMPT,
        ).run(ev.input.query)
        expanded_query = "\n".join(list(set(decomposition_query_bundle.embedding_strs)))
        await ctx.set("expanded_query", expanded_query)
        return SearchTypeEvent(input=ev.input)

    @step
    async def choose_search_type(
        self, ctx: Context, ev: SearchTypeEvent
    ) -> SemanticSearchEvent | KeywordDetectionEvent | WebSearchEvent:
        if ev.input.web_search:
            await ctx.set("search_type", SearchType.WEB_SEARCH)
            return WebSearchEvent(input=ev.input)
        if ev.input.selected_document_ids:
            await ctx.set("search_type", SearchType.KEYWORD_SEARCH)
            return KeywordSearchEvent(input=ev.input)
        response = await GEMINI_LLM.acomplete(
            SEARCH_TYPE_PROMPT.format(query=ev.input.query)
        )
        if "keyword_search" in response.text.lower():
            await ctx.set("search_type", SearchType.KEYWORD_SEARCH)
            return KeywordDetectionEvent(input=ev.input)
        await ctx.set("search_type", SearchType.SEMANTIC_SEARCH)
        return SemanticSearchEvent(input=ev.input)

    @step
    async def semantic_search(
        self, ctx: Context, ev: SemanticSearchEvent
    ) -> StreamQueryEvent:
        query_engine = CondensePlusContextChatEngine.from_defaults(
            llm=ev.input.answer_llm,
            chat_history=ev.input.chat_history,
            system_prompt=SEMANTIC_SEARCH_PROMPT.format(
                user_data=ev.input.user_data.model_dump_json()
            ),
            retriever=self.retriever_service.get_pinecone_retriever(
                organization_id=ev.input.organization_id,
                project_id=ev.input.project_id,
                filter=ev.input.filter,
                alpha=1,
            ),
            node_postprocessors=[
                RerankPostprocessor(top_n=25),
                ScoreNormalizerPostprocessor(),
                SimilarityPostprocessor(similarity_cutoff=0.5),
                FullDocumentPostprocessor(
                    project_id=ev.input.project_id,
                    document_ids=ev.input.selected_document_ids,
                ),
                EmptyResponsePostprocessor(),
            ],
            context_prompt=DEFAULT_CONTEXT_PROMPT_TEMPLATE,
            context_refine_prompt=DEFAULT_CONTEXT_REFINE_PROMPT_TEMPLATE,
            condense_prompt=DEFAULT_CONDENSE_PROMPT_TEMPLATE,
        )
        await ctx.set("query_engine", query_engine)
        return StreamQueryEvent(input=ev.input)

    @step
    async def keyword_detection(
        self, ctx: Context, ev: KeywordDetectionEvent
    ) -> KeywordSearchEvent | SemanticSearchEvent:
        document_type_values = {", ".join(DOCUMENT_TYPES)}
        document_category_values = {", ".join(DOCUMENT_CATEGORIES)}
        llm = GEMINI_LLM
        document_types_response = None
        document_categories_response = None
        entity_ids_response = None
        date_response = None

        try:
            (
                document_types_response,
                document_categories_response,
                entity_ids_response,
                date_response,
            ) = await asyncio.gather(
                llm.acomplete(
                    DOCUMENT_TYPE_DETECTION_PROMPT.format(
                        query=ev.input.query,
                        document_types=document_type_values,
                    )
                ),
                llm.acomplete(
                    DOCUMENT_CATEGORY_DETECTION_PROMPT.format(
                        query=ev.input.query,
                        document_categories=document_category_values,
                    )
                ),
                llm.acomplete(ENTITY_DETECTION_PROMPT.format(query=ev.input.query)),
                llm.acomplete(
                    DATE_DETECTION_PROMPT.format(
                        query=ev.input.query,
                        current_date=datetime.now(utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
                    )
                ),
            )
        except Exception as e:
            self.logger.error("Failed to detect keywords", e)
            raise e

        try:
            document_type_keywords = json.loads(
                document_types_response.text.replace("```json", "").replace("```", "")
            )
            document_types_list = [
                document_type.value for document_type in DocumentType
            ]
            document_types = [
                DocumentType(value=document_type)
                for document_type in document_type_keywords
                if document_type and document_type in document_types_list
            ]
        except json.JSONDecodeError:
            self.logger.error(
                f"Failed to parse document types JSON: {document_types_response.text}"
            )
            document_types = []

        try:
            document_category_keywords = json.loads(
                document_categories_response.text.replace("```json", "").replace(
                    "```", ""
                )
            )
            document_categories_list = [
                document_category.value for document_category in DocumentCategory
            ]
            document_categories = [
                DocumentCategory(value=document_category)
                for document_category in document_category_keywords
                if document_category and document_category in document_categories_list
            ]
        except json.JSONDecodeError:
            self.logger.error(
                f"Failed to parse document categories JSON: {document_categories_response.text}"
            )
            document_categories = []

        try:
            entity_id_keywords = json.loads(
                entity_ids_response.text.replace("```json", "").replace("```", "")
            )
            entity_ids = [entity_id for entity_id in entity_id_keywords if entity_id]
        except json.JSONDecodeError:
            self.logger.error(
                f"Failed to parse entity IDs JSON: {entity_ids_response.text}"
            )
            entity_ids = []

        try:
            date_range_keyword = json.loads(
                date_response.text.replace("```json", "").replace("```", "")
            )
            date_range = DateRange(**date_range_keyword) if date_range_keyword else None
        except json.JSONDecodeError:
            self.logger.error(f"Failed to parse date filter JSON: {date_response.text}")
            date_range = None

        if (
            not document_types
            and not document_categories
            and not entity_ids
            and not date_range
        ):
            await ctx.set("search_type", SearchType.SEMANTIC_SEARCH)
            return SemanticSearchEvent(input=ev.input)
        keywords = RAGSearchKeywords(
            document_types=document_types,
            document_categories=document_categories,
            entity_ids=entity_ids,
            date_range=date_range,
        )
        await ctx.set("keywords", keywords)
        return KeywordSearchEvent(input=ev.input)

    @step
    async def keyword_search(
        self,
        ctx: Context,
        ev: KeywordSearchEvent,
    ) -> StreamQueryEvent | SemanticSearchEvent:
        keywords = await ctx.get("keywords", default=None)
        print(keywords)
        documents = await self.document_repository.find_many(
            project_id=ev.input.project_id,
            document_ids=ev.input.selected_document_ids,
            limit=30,
            keywords=(
                keywords if keywords and not ev.input.selected_document_ids else None
            ),
        )
        if not documents:
            await ctx.set("search_type", SearchType.SEMANTIC_SEARCH)
            return SemanticSearchEvent(input=ev.input)
        nodes = []
        for document in documents:
            nodes.extend(document.nodes)
        query_engine = CondensePlusContextChatEngine.from_defaults(
            llm=ev.input.answer_llm,
            retriever=self.retriever_service.get_node_retriever(nodes=nodes),
            chat_history=ev.input.chat_history,
            system_prompt=KEYWORD_SEARCH_PROMPT.format(
                user_data=ev.input.user_data.model_dump_json()
            ),
            node_postprocessors=[EmptyResponsePostprocessor()],
            context_prompt=DEFAULT_CONTEXT_PROMPT_TEMPLATE,
            context_refine_prompt=DEFAULT_CONTEXT_REFINE_PROMPT_TEMPLATE,
            condense_prompt=DEFAULT_CONDENSE_PROMPT_TEMPLATE,
        )
        await ctx.set("query_engine", query_engine)
        return StreamQueryEvent(input=ev.input)

    @step
    async def web_search(self, ctx: Context, ev: WebSearchEvent) -> StreamQueryEvent:
        query_engine = CondensePlusContextChatEngine.from_defaults(
            llm=ev.input.answer_llm,
            retriever=self.retriever_service.get_web_search_retriever(
                query=ev.input.query
            ),
            chat_history=ev.input.chat_history,
            system_prompt=WEB_SEARCH_PROMPT.format(
                user_data=ev.input.user_data.model_dump_json()
            ),
            node_postprocessors=[EmptyResponsePostprocessor()],
            context_prompt=DEFAULT_CONTEXT_PROMPT_TEMPLATE,
            context_refine_prompt=DEFAULT_CONTEXT_REFINE_PROMPT_TEMPLATE,
            condense_prompt=DEFAULT_CONDENSE_PROMPT_TEMPLATE,
        )
        await ctx.set("query_engine", query_engine)
        return StreamQueryEvent(input=ev.input)

    @step
    async def stream_query(self, ctx: Context, ev: StreamQueryEvent) -> StopEvent:
        query_engine = await ctx.get("query_engine", default=None)
        expanded_query = await ctx.get("expanded_query", default=None)
        search_type = await ctx.get("search_type", default=None)
        query = (
            expanded_query
            if search_type == SearchType.KEYWORD_SEARCH and expanded_query
            else ev.input.query
        )
        stream = await query_engine.astream_chat(query)
        return StopEvent(result=stream)
