from src.models.llm import LLMModel, LLMProvider, PROVIDER_MODEL_DICT


def default_llm_provider_value(llm_provider: str | None) -> str:
    if llm_provider and llm_provider in [provider.value for provider in LLMProvider]:
        return llm_provider
    return LLMProvider.OPENAI.value


def default_llm_model_value(llm_model: str | None) -> str:
    if llm_model and llm_model in [model.value for model in LLMModel]:
        return llm_model
    return LLMModel.GPT_4_1_MINI.value


def default_llm_provider(llm_provider: str | None) -> LLMProvider:
    return LLMProvider(default_llm_provider_value(llm_provider))


def default_llm_model(llm_model: str | None) -> LLMModel:
    return LLMModel(default_llm_model_value(llm_model))


def get_provider_for_model(llm_model: str | LLMModel) -> LLMProvider:
    if isinstance(llm_model, LLMModel):
        model = llm_model
    elif isinstance(llm_model, str):
        try:
            model = LLMModel(llm_model)
        except ValueError:
            model = LLMModel.GPT_4_1_MINI
    for provider, models in PROVIDER_MODEL_DICT.items():
        if model in models:
            return provider
    return LLMProvider.OPENAI


def validate_provider_model_compatibility(
    llm_provider: str | LLMProvider | None, llm_model: str | LLMModel | None
) -> tuple[LLMProvider, LLMModel]:
    provider = default_llm_provider(llm_provider)
    model = default_llm_model(llm_model)
    model_provider = get_provider_for_model(model)
    if model_provider != provider:
        return LLMProvider.OPENAI, LLMModel.GPT_4_1_MINI
    return provider, model
