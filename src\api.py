import os

import sentry_sdk
from arize.otel import register
from fastapi import Depends, FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from llama_index.core import Settings
from openinference.instrumentation.llama_index import <PERSON>lama<PERSON>ndexInstrumentor
from pydantic_core import PydanticSerializationError
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration
from starlette.exceptions import HTTPException

from src.controllers.chat import chat_router
from src.controllers.context import context_router
from src.controllers.conversation import conversation_router
from src.controllers.document import document_router
from src.controllers.export import export_router
from src.controllers.integration import integration_router
from src.controllers.job_metadata import job_metadata_router
from src.controllers.message import message_router
from src.controllers.prompt import prompt_router
from src.controllers.settings import settings_router
from src.controllers.types import types_router
from src.controllers.user import user_router
from src.middlewares.jwt_bearer import validate_token
from src.middlewares.request_log import RequestLogMiddleware
from src.middlewares.x_api import validate_api_key
from src.rag.config import CHUNK_OVERLAP, CHUNK_SIZE, DEFAULT_EMBED_MODEL, DEFAULT_LLM
from src.util.logger import Logger

Settings.llm = DEFAULT_LLM
Settings.embed_model = DEFAULT_EMBED_MODEL
Settings.chunk_size = CHUNK_SIZE
Settings.chunk_overlap = CHUNK_OVERLAP

tracer_provider = register(
    space_id=os.environ.get("ARIZE_SPACE_ID"),
    api_key=os.environ.get("ARIZE_API_KEY"),
    project_name=os.environ.get("ARIZE_PROJECT_NAME"),
    verbose=False,
)
LlamaIndexInstrumentor().instrument(tracer_provider=tracer_provider)


def before_send(event, hint):
    if "exc_info" in hint:
        exc_type, exc_value, tb = hint["exc_info"]
        if isinstance(
            exc_value, PydanticSerializationError
        ) and "async_generator" in str(exc_value):
            return None
    return event


sentry_sdk.init(
    dsn=os.environ.get("SENTRY_DNS"),
    environment=os.environ.get("ENV"),
    send_default_pii=True,
    traces_sample_rate=1.0,
    profiles_sample_rate=1.0,
    before_send=before_send,
    attach_stacktrace=True,
    integrations=[
        FastApiIntegration(transaction_style="endpoint"),
        StarletteIntegration(transaction_style="endpoint"),
    ],
)

app = FastAPI()


@app.exception_handler(Exception)
async def global_exception_handler(_: Request, exception: Exception):
    logger = Logger("GlobalException")
    logger.exception(exception)

    if isinstance(exception, HTTPException):
        return JSONResponse(
            status_code=exception.status_code,
            content={"detail": exception.detail},
        )

    return JSONResponse(
        status_code=500,
        content={"detail": "An unexpected error occurred. Please try again later."},
    )


app.add_middleware(RequestLogMiddleware)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/", tags=["Index"])
async def index():
    return "Snapz API"


app.include_router(
    chat_router,
    tags=["Chat"],
    prefix="/chat",
)

app.include_router(
    user_router,
    tags=["Users"],
    prefix="/users",
    dependencies=[
        Depends(validate_token),
    ],
)

app.include_router(
    conversation_router,
    tags=["Conversations"],
    prefix="/conversations",
    dependencies=[Depends(validate_token)],
)

app.include_router(
    message_router,
    tags=["Messages"],
    prefix="/messages",
    dependencies=[Depends(validate_token)],
)

app.include_router(
    prompt_router,
    tags=["Prompts"],
    prefix="/prompts",
    dependencies=[Depends(validate_token)],
)

app.include_router(
    context_router,
    tags=["Contexts"],
    prefix="/contexts",
    dependencies=[Depends(validate_token)],
)

app.include_router(
    export_router,
    tags=["Exports"],
    prefix="/exports",
    dependencies=[Depends(validate_token)],
)

app.include_router(
    document_router,
    tags=["Documents"],
    prefix="/documents",
    dependencies=[Depends(validate_token)],
)

app.include_router(
    settings_router,
    tags=["Settings"],
    prefix="/settings",
    dependencies=[Depends(validate_token)],
)

app.include_router(
    types_router,
    tags=["Types"],
    prefix="/types",
    dependencies=[Depends(validate_token)],
)

app.include_router(
    integration_router,
    tags=["Integrations"],
    prefix="/integrations",
    dependencies=[Depends(validate_api_key)],
)
