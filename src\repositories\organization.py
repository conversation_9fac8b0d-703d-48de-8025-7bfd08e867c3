from bson import ObjectId
from pymongo import ReturnDocument

from src.models.organization import InsertOrganizationInput, Organization
from src.util.mongo import db


class OrganizationRepository:
    def __init__(self):
        self.collection = db.get_collection("organizations")

    def _to_model(self, organization_dict: dict) -> Organization:
        return Organization(
            id=str(organization_dict.get("_id")),
            clerk_organization_id=organization_dict.get("clerk_organization_id"),
            clerk=None,
            api_keys=organization_dict.get("api_keys"),
            created_at=organization_dict.get("created_at"),
        )

    async def find_one(
        self,
        organization_id: str | None = None,
        clerk_organization_id: str | None = None,
        api_key: str | None = None,
    ) -> Organization | None:
        filter = {}
        if organization_id:
            filter["_id"] = ObjectId(organization_id)
        if clerk_organization_id:
            filter["clerk_organization_id"] = clerk_organization_id
        if api_key:
            filter["api_keys.api_key"] = api_key
        organization_dict = await self.collection.find_one(filter=filter)
        return self._to_model(organization_dict) if organization_dict else None

    async def upsert_one(self, input: InsertOrganizationInput) -> Organization:
        organization_dict = await self.collection.find_one_and_update(
            {"clerk_organization_id": input.clerk_organization_id},
            {"$setOnInsert": input.to_dict()},
            upsert=True,
            return_document=ReturnDocument.AFTER,
        )
        return self._to_model(organization_dict)
