import os

import aiohttp

from src.util.logger import Logger


class PineconeService:
    def __init__(self):
        self.logger = Logger("PineconeService")
        self.sample_vector = [0 for _ in range(3072)]
        self.host = os.getenv("PINECONE_HOST")
        self.headers = {
            "Api-Key": os.getenv("PINECONE_API_KEY"),
            "Content-Type": "application/json",
            "X-Pinecone-API-Version": "2024-10",
        }

    async def query(
        self,
        organization_id: str,
        project_id: str | None = None,
        document_ids: list[str] | None = None,
        context_ids: list[str] | None = None,
        limit: int = 1000,
    ) -> list[dict]:
        try:
            async with aiohttp.ClientSession() as session:
                filter = {"app_organization_id": {"$eq": organization_id}}
                if project_id:
                    filter["app_project_id"] = {"$eq": project_id}
                if document_ids:
                    filter["app_document_id"] = {"$in": document_ids}
                if context_ids:
                    filter["app_context_id"] = {"$in": context_ids}
                async with session.post(
                    url=f"{self.host}/query",
                    headers=self.headers,
                    json={
                        "namespace": organization_id,
                        "vector": self.sample_vector,
                        "filter": filter,
                        "topK": limit,
                        "includeMetadata": True,
                        "includeValues": True,
                    },
                ) as response:
                    result = await response.json()
                    matches = result.get("matches")
                    return matches if matches else []
        except Exception as e:
            self.logger.error("Error finding vectors", e)
            return []

    async def upsert(
        self,
        organization_id: str,
        vectors: list[dict],
    ) -> None:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url=f"{self.host}/vectors/upsert",
                    headers=self.headers,
                    json={
                        "namespace": organization_id,
                        "vectors": vectors,
                    },
                ) as response:
                    response.raise_for_status()
        except Exception as e:
            self.logger.error("Error upserting vector", e)
            return None

    async def update_metadata(
        self,
        organization_id: str,
        vector_id: str,
        metadata: dict,
    ) -> None:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url=f"{self.host}/vectors/update",
                    headers=self.headers,
                    json={
                        "id": vector_id,
                        "namespace": organization_id,
                        "setMetadata": metadata,
                    },
                ) as response:
                    response.raise_for_status()
        except Exception as e:
            self.logger.error("Error updating metadata", e)
            return None

    async def delete(
        self,
        organization_id: str,
        project_id: str,
        document_ids: list[str] | None = None,
        context_ids: list[str] | None = None,
    ) -> None:
        try:
            vectors = await self.query(
                organization_id=organization_id,
                project_id=project_id,
                document_ids=document_ids,
                context_ids=context_ids,
            )
            if not vectors:
                return None
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url=f"{self.host}/vectors/delete",
                    headers=self.headers,
                    json={
                        "namespace": organization_id,
                        "ids": [vector.get("id") for vector in vectors],
                    },
                ) as response:
                    response.raise_for_status()
        except Exception as e:
            self.logger.error("Error deleting vectors", e)
            return None

    async def list_ids(
        self,
        organization_id: str,
        limit: int = 100,
        pagination_token: str | None = None,
    ) -> dict:
        try:
            params = {
                "namespace": organization_id,
                "limit": limit,
            }
            if pagination_token:
                params["pagination_token"] = pagination_token

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url=f"{self.host}/vectors/list",
                    headers=self.headers,
                    params=params,
                ) as response:
                    result = await response.json()
                    return result
        except Exception as e:
            self.logger.error("Error listing vector IDs", e)
            return {}

    async def fetch_by_ids(
        self,
        organization_id: str,
        ids: list[str],
    ) -> dict:
        try:
            params = {
                "namespace": organization_id,
                "ids": ids,
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url=f"{self.host}/vectors/fetch",
                    headers=self.headers,
                    params=params,
                ) as response:
                    result = await response.json()
                    return result["vectors"]
        except Exception as e:
            self.logger.error("Error fetching vectors by IDs", e)
            return {}

    async def delete_by_ids(
        self,
        organization_id: str,
        ids: list[str],
    ) -> dict:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url=f"{self.host}/vectors/delete",
                    headers=self.headers,
                    json={
                        "namespace": organization_id,
                        "ids": ids,
                    },
                ) as response:
                    response.raise_for_status()
        except Exception as e:
            self.logger.error("Error deleting vectors by IDs", e)
            return None
