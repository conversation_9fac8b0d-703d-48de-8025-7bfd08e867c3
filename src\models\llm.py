from enum import Enum


class LLMProvider(str, Enum):
    GEMINI = "gemini"
    OPENAI = "openai"
    GROQ = "groq"
    CLAUDE = "claude"
    GROK = "grok"


LLM_PROVIDERS = [provider.value for provider in LLMProvider]


class LLMModel(str, Enum):
    # Gemini models
    GEMINI_2_0_FLASH = "models/gemini-2.0-flash"
    GEMINI_2_0_FLASH_THINKING_EXP_01_21 = "models/gemini-2.0-flash-thinking-exp-01-21"
    GEMINI_2_0_FLASH_LITE = "models/gemini-2.0-flash-lite"
    # GEMINI_2_5_PRO_PREVIEW_03_25 = "models/gemini-2.5-pro-preview-03-25"
    # GEMINI_2_5_FLASH_PREVIEW_04_17 = "models/gemini-2.5-flash-preview-04-17"
    GEMINI_2_5_PRO = "models/gemini-2.5-pro"

    # OpenAI models
    O3_MINI = "o3-mini"
    O3 = "o3-2025-04-16"
    O4_MINI = "o4-mini-2025-04-16"
    GPT_4O = "gpt-4o"
    GPT_4O_MINI = "gpt-4o-mini"
    GPT_4_1 = "gpt-4.1-2025-04-14"
    GPT_4_1_MINI = "gpt-4.1-mini-2025-04-14"

    # Groq models
    LLAMA_3_3_70B_VERSATILE = "llama-3.3-70b-versatile"
    QWEN_QWQ_32B = "qwen-qwq-32b"
    DEEPSEEK_R1_DISTILL_LLAMA_70B = "deepseek-r1-distill-llama-70b"

    # Claude models
    CLAUDE_3_5_SONNET = "claude-3-5-sonnet-20241022"
    CLAUDE_3_7_SONNET = "claude-3-7-sonnet-20250219"
    CLAUDE_SONNET_4 = "claude-sonnet-4-20250514"

    # Grok models
    # GROK_3_BETA = "grok-3-beta"
    # GROK_3_MINI = "grok-3-mini"


LLM_MODELS = [model.value for model in LLMModel]

PROVIDER_MODEL_DICT = {
    LLMProvider.OPENAI: [
        LLMModel.O3_MINI,
        LLMModel.O3,
        LLMModel.O4_MINI,
        LLMModel.GPT_4O,
        LLMModel.GPT_4O_MINI,
        LLMModel.GPT_4_1,
        LLMModel.GPT_4_1_MINI,
    ],
    LLMProvider.GEMINI: [
        LLMModel.GEMINI_2_0_FLASH,
        LLMModel.GEMINI_2_0_FLASH_THINKING_EXP_01_21,
        LLMModel.GEMINI_2_0_FLASH_LITE,
        # LLMModel.GEMINI_2_5_PRO_PREVIEW_03_25,
        # LLMModel.GEMINI_2_5_FLASH_PREVIEW_04_17,
        LLMModel.GEMINI_2_5_PRO,
    ],
    LLMProvider.CLAUDE: [
        LLMModel.CLAUDE_3_5_SONNET,
        LLMModel.CLAUDE_3_7_SONNET,
        LLMModel.CLAUDE_SONNET_4,
    ],
    LLMProvider.GROQ: [
        LLMModel.LLAMA_3_3_70B_VERSATILE,
        LLMModel.QWEN_QWQ_32B,
        LLMModel.DEEPSEEK_R1_DISTILL_LLAMA_70B,
    ],
    LLMProvider.GROK: [
        # LLMModel.GROK_3_BETA,
        # LLMModel.GROK_3_MINI,
    ],
}
