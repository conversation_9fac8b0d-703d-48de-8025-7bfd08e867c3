from src.tasks.index_pending_contexts import index_pending_contexts
from src.tasks.index_pending_documents import (
    index_pending_high_priority_documents,
    index_pending_low_priority_documents,
)
from src.tasks.reset_stuck_tasks import reset_stuck_tasks
from apscheduler.jobstores.mongodb import MongoDBJobStore
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from src.util.mongo import sync_client
from src.tasks.delete_unused_embeddings import delete_unused_embeddings

scheduler = AsyncIOScheduler(
    jobstores={
        "default": MongoDBJobStore(
            database="document_db",
            collection="jobs",
            client=sync_client,
        )
    }
)


def configure_scheduler():
    scheduler.add_job(
        id="internal_index_pending_contexts",
        func=index_pending_contexts,
        trigger="interval",
        seconds=30,
        max_instances=4,
        misfire_grace_time=30,
        coalesce=True,
        replace_existing=True,
    )
    scheduler.add_job(
        id="internal_index_pending_low_priority_documents",
        func=index_pending_low_priority_documents,
        trigger="interval",
        seconds=30,
        max_instances=4,
        misfire_grace_time=30,
        coalesce=True,
        replace_existing=True,
    )
    scheduler.add_job(
        id="internal_index_pending_high_priority_documents",
        func=index_pending_high_priority_documents,
        trigger="interval",
        seconds=5,
        max_instances=4,
        misfire_grace_time=30,
        coalesce=True,
        replace_existing=True,
    )
    scheduler.add_job(
        id="internal_reset_stuck_tasks",
        func=reset_stuck_tasks,
        trigger="interval",
        seconds=60,
        max_instances=4,
        misfire_grace_time=30,
        coalesce=True,
        replace_existing=True,
    )
    scheduler.add_job(
        id="internal_delete_unused_embeddings",
        func=delete_unused_embeddings,
        trigger="interval",
        weeks=1,
        max_instances=1,
        coalesce=True,
        replace_existing=True,
    )
