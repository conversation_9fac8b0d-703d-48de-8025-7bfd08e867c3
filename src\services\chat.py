import asyncio
import json
from datetime import datetime, timed<PERSON><PERSON>

from fastapi import WebSocket, WebSocketException
from llama_index.core.chat_engine.types import StreamingAgentChatResponse
from llama_index.core.llms import ChatMessage, MessageRole
from pytz import utc

from src.repositories.settings import SettingsRepository
from src.services.settings import SettingsService
from src.models.auth import UserData
from src.models.chat import ChatMessageRequest, ChatMessageResponse
from src.models.conversation import (
    DEFAULT_CONVERSATION_TITLE,
    Conversation,
    DocumentScore,
    InsertDocumentScoreInput,
    UpdateConversationInput,
)
from src.models.document import Document
from src.models.llama_index import RetrieverFilter
from src.models.message import InsertMessageInput, Message, MessageType
from src.models.shared import to_iso_8601
from src.repositories.conversation import ConversationRepository
from src.repositories.document import DocumentRepository
from src.repositories.organization import OrganizationRepository
from src.repositories.project import ProjectRepository
from src.services.conversation import ConversationService
from src.util.logger import Logger
from src.workflows.rag_query import RAGQueryInput, RAGQueryWorkflow


class ChatService:
    def __init__(self, websocket: WebSocket):
        self.logger = Logger("ChatService")
        self.websocket = websocket
        self.rag_query_workflow = RAGQueryWorkflow()
        self.project_repository = ProjectRepository()
        self.organization_repository = OrganizationRepository()
        self.document_repository = DocumentRepository()
        self.conversation_repository = ConversationRepository()
        self.conversation_service = ConversationService()
        self.settings_repository = SettingsRepository()
        self.settings_service = SettingsService()

    async def rag_query(
        self,
        organization_id: str,
        project_id: str,
        request: ChatMessageRequest,
        user_data: UserData,
    ) -> None:
        async def get_selected_documents() -> list[Document]:
            if request.filters and request.filters.document_ids:
                return await self.document_repository.find_many(
                    project_id=project_id,
                    document_ids=request.filters.document_ids,
                )
            return []

        (
            conversation,
            selected_documents,
        ) = await asyncio.gather(
            self.conversation_repository.find_one(
                project_id=project_id,
                conversation_id=request.conversation_id,
            ),
            get_selected_documents(),
        )

        if not conversation:
            raise WebSocketException(code=3003, reason="Conversation not found")

        selected_document_ids = [document.id for document in selected_documents]
        chat_history = self.get_chat_history(request.text, conversation.messages)

        query_result = None
        title = None
        try:
            (settings, answer_llm) = await asyncio.gather(
                self.settings_repository.find_one(
                    project_id=project_id,
                    user_id=conversation.user_id,
                ),
                self.settings_service.get_llm(
                    project_id=project_id,
                    user_id=conversation.user_id,
                ),
            )
            (query_result, title) = await asyncio.gather(
                self.rag_query_workflow.run(
                    input=RAGQueryInput(
                        query=request.text,
                        organization_id=organization_id,
                        project_id=project_id,
                        selected_document_ids=selected_document_ids,
                        filter=RetrieverFilter(document_ids=selected_document_ids),
                        user_data=user_data,
                        chat_history=chat_history,
                        answer_llm=answer_llm,
                        web_search=request.web_search,
                    )
                ),
                self.generate_conversation_title(
                    conversation=conversation,
                    query=request.text,
                ),
            )
        except Exception as e:
            self.logger.error(f"Error running RAG query: {e}")
            raise WebSocketException(code=3003, reason="Error running RAG query")

        if not query_result:
            raise WebSocketException(code=3003, reason="No results found")

        answer = ""
        async for chunk in query_result.async_response_gen():
            if chunk:
                answer += chunk
                await self.send_message(
                    ChatMessageResponse(
                        conversation_id=conversation.id,
                        type=MessageType.TEXT,
                        end=False,
                        text=chunk,
                    )
                )

        scores = await self.get_scores(project_id, query_result)
        score_inputs = [
            InsertDocumentScoreInput(
                document_id=score.document_id,
                name=score.name,
                slug=score.slug,
                size=score.size,
                url=score.url,
                content_type=score.content_type,
                text=score.text,
                score=score.score,
                source_url=score.source_url,
            )
            for score in scores
        ]

        await self.conversation_repository.update_one(
            conversation_id=conversation.id,
            project_id=project_id,
            user_id=conversation.user_id,
            input=UpdateConversationInput(
                title=title,
                add_scores=score_inputs,
                add_messages=[
                    InsertMessageInput(
                        is_user=True,
                        type=MessageType.TEXT,
                        text=request.text,
                        created_at=datetime.now(utc),
                    ),
                    InsertMessageInput(
                        is_user=False,
                        type=MessageType.TEXT,
                        text=answer,
                        llm_provider=settings.llm_provider,
                        llm_model=settings.llm_model,
                        created_at=datetime.now(utc) + timedelta(seconds=2),
                    ),
                ],
            ),
        )

        asyncio.create_task(
            self.send_message(
                ChatMessageResponse(
                    conversation_id=request.conversation_id,
                    end=True,
                    type=MessageType.TEXT,
                    text=answer,
                    scores=scores,
                )
            ),
        )

    async def generate_conversation_title(
        self, conversation: Conversation, query: str
    ) -> str:
        if conversation.title == DEFAULT_CONVERSATION_TITLE:
            return await self.conversation_service.generate_conversation_title(query)
        return conversation.title

    async def get_scores(
        self,
        project_id: str,
        query_result: StreamingAgentChatResponse,
    ) -> list[DocumentScore]:
        nodes = {}
        document_ids = []
        for node in query_result.source_nodes:
            document_id = node.metadata.get("app_document_id")
            if not document_id:
                continue
            if document_id not in nodes.keys() or (
                document_id in nodes.keys() and node.score > nodes[document_id]["score"]
            ):
                nodes[document_id] = {"score": node.score, "text": node.text}
                document_ids.append(document_id)

        scores = []
        if document_ids:
            documents = await self.document_repository.find_many(
                project_id=project_id,
                document_ids=document_ids,
            )
            for document_id, node in nodes.items():
                document = next(
                    (document for document in documents if document.id == document_id),
                    None,
                )
                if document:
                    scores.append(
                        DocumentScore(
                            document_id=document.id,
                            name=document.name,
                            slug=document.slug,
                            size=document.size,
                            url=document.url,
                            content_type=document.content_type,
                            text=node.get("text"),
                            score=node.get("score"),
                            source_url=document.source_url,
                        )
                    )

        return scores

    def get_chat_history(
        self,
        query: str,
        messages: list[Message] = [],
    ) -> list[ChatMessage]:
        chat_history = []
        for message in messages:
            role = MessageRole.USER if message.is_user else MessageRole.ASSISTANT
            if message.type == MessageType.TEXT and message.text is not None:
                chat_history.append(
                    ChatMessage(
                        role=role,
                        content=message.text,
                    )
                )
            elif (
                message.type == MessageType.FILE
                and message.documents
                and len(message.documents) > 0
            ):
                chat_history.append(
                    ChatMessage(
                        role=role,
                        content="Documents uploaded: "
                        + ", ".join([document.name for document in message.documents]),
                    )
                )
        if (
            len(chat_history) > 0
            and chat_history[-1].role == MessageRole.USER
            and chat_history[-1].content == query
        ):
            chat_history.pop()
        return chat_history

    async def send_message(self, message: ChatMessageResponse) -> None:
        try:
            await self.websocket.send_json(message.model_dump_json())
            await asyncio.sleep(0)
        except Exception:
            pass

    async def send_json(self, data: dict) -> None:
        try:
            await self.websocket.send_json(
                json.dumps(data, default=self.json_serializer)
            )
            await asyncio.sleep(0)
        except Exception:
            pass

    def json_serializer(self, obj):
        if isinstance(obj, datetime):
            return to_iso_8601(obj)
        return obj
