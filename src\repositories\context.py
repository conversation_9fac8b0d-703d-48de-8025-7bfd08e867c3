import asyncio
from datetime import datetime

from bson import ObjectId
from dateutil.parser import isoparse
from pymongo import DESCENDING

from src.models.context import Context, InsertContextInput, UpdateContextInput
from src.models.shared import Pagination, PaginationResponse, PaginationResult
from src.util.date import mongo_date
from src.util.mongo import db


class ContextRepository:
    def __init__(self):
        self.collection = db.get_collection("contexts")

    def _to_model(self, context_dict: dict) -> Context:
        conversation_id = context_dict.get("conversation_id")
        if conversation_id:
            conversation_id = str(conversation_id)
        document_id = context_dict.get("document_id")
        if document_id:
            document_id = str(document_id)
        user_id = context_dict.get("user_id")
        if user_id:
            user_id = str(user_id)
        message_id = context_dict.get("message_id")
        if message_id:
            message_id = str(message_id)
        return Context(
            id=str(context_dict.get("_id")),
            project_id=str(context_dict.get("project_id")),
            text=context_dict.get("text"),
            conversation_id=conversation_id,
            user_id=user_id,
            external_id=context_dict.get("external_id"),
            document_id=document_id,
            message_id=message_id,
            created_at=context_dict.get("created_at"),
        )

    async def find_one(
        self,
        context_id: str | None = None,
        project_id: str | None = None,
        conversation_id: str | None = None,
        user_id: str | None = None,
        external_id: str | None = None,
        message_id: str | None = None,
    ) -> Context | None:
        filter = {}
        if context_id:
            filter["_id"] = ObjectId(context_id)
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if conversation_id:
            filter["conversation_id"] = ObjectId(conversation_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        if external_id:
            filter["external_id"] = external_id
        if message_id:
            filter["message_id"] = ObjectId(message_id)
        context_dict = await self.collection.find_one(filter=filter)
        return self._to_model(context_dict) if context_dict else None

    async def find_many(
        self,
        context_ids: list[str] = [],
        external_ids: list[str] = [],
        document_ids: list[str] = [],
        project_id: str | None = None,
        conversation_id: str | None = None,
        user_id: str | None = None,
        message_id: str | None = None,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[Context]:
        filter = {}
        if context_ids:
            filter["_id"] = {
                "$in": [ObjectId(context_id) for context_id in context_ids]
            }
        if document_ids:
            filter["document_id"] = {
                "$in": [ObjectId(document_id) for document_id in document_ids]
            }
        if external_ids:
            filter["external_id"] = {"$in": external_ids}
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if conversation_id:
            filter["conversation_id"] = ObjectId(conversation_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        if message_id:
            filter["message_id"] = ObjectId(message_id)
        context_dicts = await self.collection.find(filter=filter, sort=sort).to_list()
        return [self._to_model(context_dict) for context_dict in context_dicts]

    async def paginate(
        self,
        project_id: str | None = None,
        context_ids: list[str] = [],
        external_ids: list[str] = [],
        user_id: str | None = None,
        conversation_id: str | None = None,
        message_id: str | None = None,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        page_size: int = 10,
        page_number: int = 1,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> PaginationResponse[Context]:
        date_filter = {}
        if start_date:
            date_filter["$gte"] = mongo_date(start_date)
        if end_date:
            date_filter["$lte"] = mongo_date(end_date)

        filter = {}
        if project_id:
            filter = {"project_id": ObjectId(project_id)}
        if context_ids:
            filter["_id"] = {
                "$in": [ObjectId(context_id) for context_id in context_ids]
            }
        if external_ids:
            filter["external_id"] = {"$in": external_ids}
        if conversation_id:
            filter["conversation_id"] = ObjectId(conversation_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        if message_id:
            filter["message_id"] = ObjectId(message_id)
        if date_filter:
            filter["created_at"] = date_filter

        pagination = Pagination(page_size=page_size, page_number=page_number)

        (total, context_dicts) = await asyncio.gather(
            self.collection.count_documents(filter),
            self.collection.find(
                filter=filter,
                limit=pagination.limit(),
                skip=pagination.skip(),
                sort=sort,
            ).to_list(),
        )

        return PaginationResponse(
            data=[self._to_model(context_dict) for context_dict in context_dicts],
            pagination=PaginationResult(
                page_size=page_size,
                page_number=page_number,
                total=total,
            ),
        )

    async def insert_one(self, input: InsertContextInput) -> Context | None:
        result = await self.collection.insert_one(input.to_dict())
        return await self.find_one(context_id=result.inserted_id)

    async def insert_many(
        self,
        inputs: list[InsertContextInput],
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[Context]:
        context_dicts = [input.to_dict() for input in inputs]
        result = await self.collection.insert_many(context_dicts)
        return await self.find_many(context_ids=result.inserted_ids, sort=sort)

    async def update_one(
        self,
        context_id: str,
        input: UpdateContextInput,
        project_id: str | None = None,
    ) -> Context | None:
        filter = {"_id": ObjectId(context_id)}
        if project_id:
            filter["project_id"] = ObjectId(project_id)

        update = {}
        if input.text:
            update["text"] = input.text
        if input.conversation_id:
            update["conversation_id"] = ObjectId(input.conversation_id)
        if input.document_id:
            update["document_id"] = ObjectId(input.document_id)
        if input.user_id:
            update["user_id"] = input.user_id
        if input.external_id:
            update["external_id"] = input.external_id
        if input.message_id:
            update["message_id"] = ObjectId(input.message_id)

        await self.collection.update_one(filter=filter, update={"$set": update})
        return await self.find_one(context_id=context_id, project_id=project_id)

    async def delete_one(
        self,
        context_id: str | None = None,
        project_id: str | None = None,
        conversation_id: str | None = None,
        user_id: str | None = None,
        external_id: str | None = None,
    ) -> Context | None:
        context = await self.find_one(
            context_id=context_id,
            project_id=project_id,
            conversation_id=conversation_id,
            user_id=user_id,
            external_id=external_id,
        )
        if not context:
            return None
        await self.collection.delete_one(filter={"_id": ObjectId(context.id)})
        return context

    async def delete_many(
        self,
        context_ids: list[str] = [],
        external_ids: list[str] = [],
        document_ids: list[str] = [],
        project_id: str | None = None,
        conversation_id: str | None = None,
        user_id: str | None = None,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[Context]:
        contexts = await self.find_many(
            context_ids=context_ids,
            external_ids=external_ids,
            document_ids=document_ids,
            project_id=project_id,
            conversation_id=conversation_id,
            user_id=user_id,
            sort=sort,
        )
        if not contexts:
            return []
        await self.collection.delete_many(
            filter={"_id": {"$in": [ObjectId(context.id) for context in contexts]}}
        )
        return contexts
