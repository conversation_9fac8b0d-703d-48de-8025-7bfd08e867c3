---
description: Repository Information Overview
alwaysApply: true
---

# Snapz.ai Document Processing API Information

## Summary
Snapz.ai is a sophisticated document processing API built with Python that leverages advanced AI capabilities for document intake, analysis, and querying. The system implements a Retrieval-Augmented Generation (RAG) architecture to provide intelligent document processing and information retrieval.

## Structure
- **src/**: Core application code
  - **controllers/**: API request handlers
  - **middlewares/**: Custom middleware components
  - **models/**: Data models and schemas
  - **repositories/**: Data access layer
  - **services/**: Business logic implementation
  - **tasks/**: Background task definitions
  - **rag/**: RAG implementation components
  - **util/**: Utility functions and helpers
  - **workflows/**: Workflow definitions
- **build/**: Docker build configurations
- **env/**: Environment configuration files
- **docs/**: Documentation files

## Language & Runtime
**Language**: Python
**Version**: 3.11
**Framework**: FastAPI 0.115.5
**Package Manager**: Pipenv

## Dependencies
**Main Dependencies**:
- FastAPI (0.115.5): Web framework
- Pydantic (2.9.2): Data validation
- Uvicorn (0.32.1): ASGI server
- MongoDB (pymongo 4.9.2, motor 3.6.0): Database
- LlamaIndex (0.13.0): Document indexing
- OpenAI (1.75.0): LLM integration
- APScheduler (3.10.4): Task scheduling

**External Services**:
- MongoDB: Document storage
- Pinecone: Vector database for embeddings
- OpenAI: LLM and embeddings
- Cohere: Result reranking (optional)

## Build & Installation
```bash
# Install dependencies
pipenv install

# Run API locally
pipenv run api

# Run worker locally
pipenv run worker
```

## Docker
**Main Dockerfile**: Dockerfile
**Service Dockerfiles**: 
- build/api/Dockerfile
- build/worker/Dockerfile

**Configuration**: Docker Compose with three services:
- mongodb: MongoDB database
- api: FastAPI application
- worker: Background task processor

**Run Command**:
```bash
docker-compose up -d
```

## Application Components
**API Service**: Handles HTTP requests, document processing, and user interactions
- Entry Point: src/api.py
- Endpoints: Users, Documents, Conversations, Contexts, etc.

**Worker Service**: Manages background tasks and scheduled jobs
- Entry Point: src/worker.py
- Scheduler: APScheduler with MongoDB job store
- Tasks: Document indexing, context processing, cleanup jobs

## RAG Implementation
**Document Processing**: 
- Chunking (configurable size and overlap)
- Embedding generation via OpenAI
- Vector storage in Pinecone

**Query Processing**:
- Retrieval from vector store
- Reranking with Cohere (optional)
- Response generation with LLMs

## Authentication
**Method**: JWT-based authentication
**Implementation**: Custom JWT bearer middleware

## Release Process
**Development**:
```bash
pipenv run release-dev
```

**Production**:
```bash
git checkout main
git pull
git checkout release/prod
git reset --hard origin/main
git push
```