import asyncio
import os

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient

from src.services.pinecone import PineconeService

client = AsyncIOMotorClient(os.environ.get("MONGO_URI"))
db = client["document_db"]
tasks_collection = db["tasks"]
contexts_collection = db["contexts"]
organization_id = ""
project_id = ""

documents_collection = db["documents"]
contexts_collection = db["contexts"]
tasks_collection = db["tasks"]
conversations_collection = db["conversations"]
prompts_collection = db["prompts"]
pinecone_service = PineconeService()


async def delete_documents():
    print("deleting documents")
    documents = await documents_collection.find(
        {"project_id": ObjectId(project_id)}
    ).to_list()
    document_ids = [ObjectId(document["_id"]) for document in documents]
    await tasks_collection.delete_many({"document_id": {"$in": document_ids}})
    await documents_collection.delete_many({"project_id": ObjectId(project_id)})
    print("deleted documents")


async def delete_contexts():
    print("deleting contexts")
    contexts = await contexts_collection.find(
        {"project_id": ObjectId(project_id)}
    ).to_list()
    context_ids = [ObjectId(context["_id"]) for context in contexts]
    await tasks_collection.delete_many({"context_id": {"$in": context_ids}})
    await contexts_collection.delete_many({"project_id": ObjectId(project_id)})
    print("deleted contexts")


async def delete_conversations():
    print("deleting conversations")
    await conversations_collection.delete_many({"project_id": ObjectId(project_id)})
    print("deleted conversations")


async def delete_prompts():
    print("deleting prompts")
    await prompts_collection.delete_many({"project_id": ObjectId(project_id)})
    print("deleted prompts")


async def delete_embeddings():
    print("deleting embeddings")
    await pinecone_service.delete(organization_id, project_id)
    print("deleted embeddings")


async def delete_project_data():
    await asyncio.gather(
        delete_documents(),
        delete_contexts(),
        delete_conversations(),
        delete_prompts(),
        delete_embeddings(),
    )


asyncio.run(delete_project_data())
