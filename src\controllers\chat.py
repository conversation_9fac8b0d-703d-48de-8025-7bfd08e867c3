from fastapi import <PERSON><PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, WebSocketException

from src.models.chat import Chat<PERSON><PERSON>ageFilter
from src.services.auth import AuthService
from src.services.chat import ChatMessageRequest, ChatService
from src.services.conversation import ConversationService
from src.util.connection_manager import ConnectionManager
from src.util.logger import Logger

chat_router = APIRouter()
connection_manager = ConnectionManager()
conversation_service = ConversationService()
auth_service = AuthService()
logger = Logger("ChatController")


@chat_router.websocket("")
async def chat_socket(websocket: WebSocket, token: str):
    chat_service = ChatService(websocket)
    auth = None

    try:
        if not token:
            raise WebSocketException(code=3000, reason="token parameter is required")
        auth = await auth_service.authenticate_by_token(token)
        connected = await connection_manager.connect(
            websocket=websocket,
            client_id=auth.user.id,
            organization_id=auth.organization.id,
            project_id=auth.project.id,
        )
        if not connected:
            return
    except Exception as e:
        logger.error("Error authenticating websocket", e)
        await connection_manager.close(websocket)
        raise WebSocketException(code=3000, reason="Error authenticating websocket")

    while True:
        try:
            data = await websocket.receive_json()
            if "ping" in data and data.get("ping"):
                await chat_service.send_json({"pong": True})
                continue
            conversation_id = data.get("conversationId")
            if not conversation_id:
                raise WebSocketException(
                    code=1003, reason="Input conversation ID is required"
                )
            text = data.get("text")
            if not text:
                raise WebSocketException(code=1003, reason="Input text is required")
            filters = data.get("filters")
            web_search = data.get("webSearch", False)
            request = ChatMessageRequest(
                conversation_id=conversation_id,
                text=text,
                filters=ChatMessageFilter(**filters) if filters else None,
                web_search=web_search,
            )
            await chat_service.rag_query(
                organization_id=auth.organization.id,
                project_id=auth.project.id,
                request=request,
                user_data=auth.get_user_data(),
            )
        except WebSocketDisconnect:
            await connection_manager.disconnect(
                organization_id=auth.organization.id,
                project_id=auth.project.id,
                client_id=auth.user.id,
            )
            return
        except WebSocketException as e:
            logger.error("Error processing message", e)
            await chat_service.send_json(
                {"error": {"code": e.code, "reason": e.reason}}
            )
        except Exception as e:
            logger.error("Websocket error", e)
            return
