from src.models.clerk import ClerkOrganizationRole
from src.models.organization import Organization
from src.models.project import Project
from src.models.settings import Settings
from src.models.shared import ConfigModel
from src.models.user import User


class TokenData(ConfigModel):
    clerk_user_id: str
    clerk_organization_id: str
    organization_role: str


class UserData(ConfigModel):
    user_id: str | None = None
    user_first_name: str | None = None
    user_last_name: str | None = None
    user_email: str | None = None
    project_id: str | None = None
    project_name: str | None = None
    organization_id: str | None = None
    organization_name: str | None = None


class Auth(ConfigModel):
    organization: Organization
    project: Project
    user: User | None = None
    settings: Settings | None = None

    def is_integration(self) -> bool:
        return not bool(self.user)

    def is_admin(self) -> bool:
        if self.is_integration():
            return False
        for membership in self.user.memberships:
            if membership.organization.id == self.organization.clerk_organization_id:
                return membership.role == ClerkOrganizationRole.ADMIN
        return False

    def is_member(self) -> bool:
        if self.is_integration():
            return False
        for membership in self.user.memberships:
            if membership.organization.id == self.organization.clerk_organization_id:
                return membership.role == ClerkOrganizationRole.MEMBER
        return False

    def has_permission(self, user_id_to_update: str | None = None) -> bool:
        if self.is_integration() or self.is_admin():
            return True
        if not user_id_to_update and self.is_member():
            return False
        return self.user.id == user_id_to_update

    def get_user_id(self, user_id_to_update: str | None = None) -> str | None:
        if self.is_integration():
            return user_id_to_update
        if self.is_admin():
            return user_id_to_update or self.user.id
        if self.is_member():
            return self.user.id
        return None

    def get_member_user_id(self, user_id_to_update: str | None = None) -> str | None:
        """
        If the user is an admin, they can update any user.
        If the user is a member, they can only update themselves.
        """
        if self.is_integration() or self.is_admin():
            return user_id_to_update
        if self.is_member():
            return self.user.id
        return None

    def get_user_data(self) -> UserData:
        return UserData(
            user_id=self.user.id if self.user else None,
            user_first_name=(
                self.user.clerk.first_name if self.user and self.user.clerk else None
            ),
            user_last_name=(
                self.user.clerk.last_name if self.user and self.user.clerk else None
            ),
            user_email=self.user.clerk.email if self.user and self.user.clerk else None,
            project_id=self.project.id,
            project_name=self.project.name,
            organization_id=self.organization.id,
            organization_name=(
                self.organization.clerk.name if self.organization.clerk else None
            ),
        )
