from datetime import datetime
from enum import Enum

from src.models.document import (
    DOCUMENT_CATEGORIES,
    DOCUMENT_TYPES,
    DocumentCategory,
    DocumentType,
)
from src.models.shared import ConfigModel


class SearchType(Enum):
    SEMANTIC_SEARCH = "semantic_search"
    KEYWORD_SEARCH = "keyword_search"
    WEB_SEARCH = "web_search"


class ActionType(Enum):
    QUESTION_ANSWER = "question_answer"
    DOCUMENT_AGGREGATION = "document_aggregation"


class DateRange(ConfigModel):
    start_date: datetime
    end_date: datetime


class RAGSearchKeywords(ConfigModel):
    document_types: list[DocumentType] = []
    document_categories: list[DocumentCategory] = []
    entity_ids: list[str] = []
    date_range: DateRange | None = None

    @staticmethod
    def from_dict(keywords: dict | None):
        if not keywords:
            return None
        document_types = [
            DocumentType(value=document_type)
            for document_type in keywords.get("document_types")
            if document_type in DOCUMENT_TYPES
        ]
        document_categories = [
            DocumentCategory(value=document_category)
            for document_category in keywords.get("document_categories")
            if document_category in DOCUMENT_CATEGORIES
        ]
        entity_ids = [
            entity_id for entity_id in keywords.get("entity_ids") if entity_id
        ]
        date_range = (
            DateRange(**keywords.get("date_range"))
            if keywords.get("date_range")
            else None
        )
        return RAGSearchKeywords(
            document_types=document_types,
            document_categories=document_categories,
            entity_ids=entity_ids,
            date_range=date_range,
        )


class RAGSearch(ConfigModel):
    search_type: SearchType
    action_type: ActionType
    keywords: RAGSearchKeywords
