import asyncio

from src.models.auth import Auth
from src.models.context import Delete<PERSON>ontextsBody
from src.models.document import DeleteDocumentsBody
from src.models.integration import ExternalDeleteResponse, ExternalResponse
from src.services.context import ContextService
from src.services.document import DocumentService


class IntegrationService:
    def __init__(self):
        self.document_service = DocumentService()
        self.context_service = ContextService()

    async def get_external(self, auth: Auth, external_id: str) -> ExternalResponse:
        (documents, contexts) = await asyncio.gather(
            self.document_service.get_many(
                auth=auth,
                external_ids=[external_id],
            ),
            self.context_service.get_many(
                auth=auth,
                external_ids=[external_id],
            ),
        )

        return ExternalResponse(
            documents=documents,
            contexts=contexts,
        )

    async def delete_external(self, auth: Auth, external_id: str) -> ExternalResponse:
        (documents_response, contexts_response) = await asyncio.gather(
            self.document_service.delete_many(
                auth=auth,
                body=DeleteDocumentsBody(external_ids=[external_id]),
            ),
            self.context_service.delete_many(
                auth=auth,
                body=DeleteContextsBody(external_ids=[external_id]),
            ),
        )

        return ExternalDeleteResponse(
            documents=documents_response,
            contexts=contexts_response,
        )
