import asyncio

from src.models.document import DocumentType
from src.workflows.uploaded_document_summary import (
    UploadedDocumentSummaryInput,
    UploadedDocumentSummaryWorkflow,
)


async def main():
    workflow = UploadedDocumentSummaryWorkflow()
    result = await workflow.run(
        input=UploadedDocumentSummaryInput(
            project_id="67a6793020583a70a157861f",
            document_types=[DocumentType.INVOICE_RECEIPT, DocumentType.EXPENSE],
            days_back=7,
        )
    )
    print(result)
    with open("samples/output.pdf", "wb") as f:
        f.write(result.pdf_data)


if __name__ == "__main__":
    asyncio.run(main())
