from bson import ObjectId

from src.models.export import Export, InsertExportInput
from src.util.mongo import db


class ExportRepository:
    def __init__(self):
        self.collection = db.get_collection("exports")

    def _to_model(self, export_dict: dict) -> Export:
        return Export(
            id=str(export_dict.get("_id")),
            user_id=str(export_dict.get("user_id")),
            url=export_dict.get("url"),
            created_at=export_dict.get("created_at"),
        )
    
    async def find_one(self, export_id: str) -> Export | None:
        export_dict = await self.collection.find_one({"_id": ObjectId(export_id)})
        return self._to_model(export_dict) if export_dict else None

    async def insert_one(self, input: InsertExportInput) -> Export:
        result = await self.collection.insert_one(input.to_dict())
        return await self.find_one(export_id=result.inserted_id)