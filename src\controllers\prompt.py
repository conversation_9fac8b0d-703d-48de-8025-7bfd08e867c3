from fastapi import APIRouter, Query, Request

from src.models.prompt import (
    DeletePromptsBody,
    PatchPromptBody,
    PostPromptBody,
    PromptResponse,
)
from src.models.shared import DeleteResponse, PaginationResponse
from src.services.prompt import PromptService

prompt_router = APIRouter()
prompt_service = PromptService()


@prompt_router.get("/paginate", response_model=PaginationResponse[PromptResponse])
async def paginate(
    request: Request,
    name: str | None = None,
    page_size: int = 10,
    page_number: int = 1,
):
    return await prompt_service.paginate(
        auth=request.state.auth,
        name=name,
        page_size=page_size,
        page_number=page_number,
    )


@prompt_router.get("/{prompt_id}", response_model=PromptResponse)
async def get_one(request: Request, prompt_id: str):
    return await prompt_service.get_one(
        auth=request.state.auth,
        prompt_id=prompt_id,
    )


@prompt_router.get("", response_model=list[PromptResponse])
async def get_many(
    request: Request,
    prompt_ids: list[str] = Query([]),
):
    return await prompt_service.get_many(
        auth=request.state.auth,
        prompt_ids=prompt_ids,
    )


@prompt_router.post("", response_model=PromptResponse)
async def post_one(
    request: Request,
    body: PostPromptBody,
):
    return await prompt_service.post_one(
        auth=request.state.auth,
        body=body,
    )


@prompt_router.patch("/{prompt_id}", response_model=PromptResponse)
async def patch_one(
    request: Request,
    prompt_id: str,
    body: PatchPromptBody,
):
    return await prompt_service.patch_one(
        auth=request.state.auth,
        prompt_id=prompt_id,
        body=body,
    )


@prompt_router.delete("/{prompt_id}", response_model=DeleteResponse)
async def delete_one(request: Request, prompt_id: str):
    return await prompt_service.delete_one(
        auth=request.state.auth,
        prompt_id=prompt_id,
    )


@prompt_router.delete("", response_model=DeleteResponse)
async def delete_many(
    request: Request,
    body: DeletePromptsBody,
):
    return await prompt_service.delete_many(
        auth=request.state.auth,
        body=body,
    )
