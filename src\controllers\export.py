from datetime import datetime

from fastapi import APIRouter, Request

from src.models.export import ExportResponse
from src.services.export import ExportService

export_router = APIRouter()
export_service = ExportService()


@export_router.post("/messages", response_model=ExportResponse)
async def export_user_queries(
    request: Request,
    start_date: datetime | None = None,
    end_date: datetime | None = None,
    user_id: str | None = None,
):
    return await export_service.export_user_queries(
        auth=request.state.auth,
        user_id=user_id,
        start_date=start_date,
        end_date=end_date,
    )

@export_router.post("/file_uploads", response_model=ExportResponse)
async def export_file_uploads(
    request: Request,
    start_date: datetime | None = None,
    end_date: datetime | None = None,
    user_id: str | None = None,
):
    return await export_service.export_file_uploads(
        auth=request.state.auth,
        user_id=user_id,
        start_date=start_date,
        end_date=end_date,
    )
