from fastapi import APIRouter, Request

from src.models.context import (
    ContextResponse,
    PostContextBody,
)
from src.services.context import ContextService

context_router = APIRouter()
context_service = ContextService()


@context_router.post("", response_model=ContextResponse)
async def post_one(
    request: Request,
    body: PostContextBody,
):
    return await context_service.create(
        auth=request.state.auth,
        text=body.text,
        conversation_id=body.conversation_id,
        external_id=body.external_id,
        document_id=body.document_id,
        message_id=body.message_id,
    ) 