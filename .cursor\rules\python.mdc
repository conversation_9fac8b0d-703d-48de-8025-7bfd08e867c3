---
description: Python rules
globs: *.py
alwaysApply: false
---
# Python Best Practices

## Project Structure

- Use src-layout with `src/type_of_file/file` using controllers, services, repositories and models.
- Store requirements in `Pipfile`

## Code Style

- Follow Black code formatting
- Use isort for import sorting
- Follow PEP 8 naming conventions:
  - snake_case for functions and variables
  - PascalCase for classes
  - UPPER_CASE for constants
- Maximum line length of 88 characters (Black default)
- Use absolute imports over relative imports starting with `src.`

## Type Hints

- Use type hints for all function parameters and returns
- Do not import types from `typing` module, use native types instead.
- Use `Type | None` instead of `Optional[Type]`
- Use `TypeVar` for generic types

## API Design

- Implement proper request validation
- Use proper HTTP status codes
- Handle errors consistently
- Use proper response formats
- Implement proper rate limiting

## Security

- Use HTTPS in production
- Implement proper CORS
- Sanitize all user inputs
- Use proper session configuration
- Implement proper logging
- Follow OWASP guidelines

## Performance

- Implement database query optimization
- Use proper connection pooling
- Implement proper pagination
- Use background tasks for heavy operations
- Monitor application performance

## Error Handling

- Do not create custom exception classes
- Use proper try-except blocks
- Implement proper logging
- Return proper error responses
- Handle edge cases properly
- Use proper error messages

## Documentation

- Use Google-style docstrings
- Keep README.md updated
- Use proper inline comments
- Generate API documentation
- Document environment setup