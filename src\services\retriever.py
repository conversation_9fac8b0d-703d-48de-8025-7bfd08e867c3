import os

from llama_index.core import VectorStoreIndex
from llama_index.core.retrievers import BaseRetriever, VectorIndexRetriever
from llama_index.core.schema import Node<PERSON>ithScore, TextNode
from llama_index.core.vector_stores import (
    FilterOperator,
    MetadataFilter,
    MetadataFilters,
)
from llama_index.core.vector_stores.types import VectorStoreQueryMode
from llama_index.tools.tavily_research.base import TavilyToolSpec
from llama_index.vector_stores.pinecone import PineconeVectorStore

from src.models.llama_index import RetrieverFilter
from src.rag.config import OPEN_AI_EMBED_MODEL


class NodeRetriever(BaseRetriever):
    def __init__(self, nodes: list[NodeWithScore]):
        self.nodes = nodes

    def _retrieve(self, query: str) -> list[NodeWithScore]:
        return self.nodes


class RetrieverService:
    def get_vector_store(self, organization_id: str) -> PineconeVectorStore:
        return PineconeVectorStore(
            index_name=os.getenv("PINECONE_INDEX"),
            namespace=organization_id,
            add_sparse_vector=True,
        )

    def get_index(self, organization_id: str) -> VectorStoreIndex:
        return VectorStoreIndex.from_vector_store(
            vector_store=self.get_vector_store(organization_id),
            embed_model=OPEN_AI_EMBED_MODEL,
        )

    def get_pinecone_retriever(
        self,
        organization_id: str,
        project_id: str,
        filter: RetrieverFilter,
        alpha: float = 1.0,
        top_k: int = 50,
    ) -> VectorIndexRetriever:
        filters = [
            MetadataFilter(
                key="app_organization_id",
                operator=FilterOperator.EQ,
                value=organization_id,
            ),
            MetadataFilter(
                key="app_project_id",
                operator=FilterOperator.EQ,
                value=project_id,
            ),
        ]

        if filter.document_ids:
            filters.append(
                MetadataFilter(
                    key="app_document_id",
                    operator=FilterOperator.IN,
                    value=filter.document_ids,
                ),
            )
        if filter.external_ids:
            filters.append(
                MetadataFilter(
                    key="app_external_id",
                    operator=FilterOperator.IN,
                    value=filter.external_ids,
                ),
            )
        if filter.user_id:
            filters.append(
                MetadataFilter(
                    key="app_user_id",
                    operator=FilterOperator.EQ,
                    value=filter.user_id,
                ),
            )
        if filter.conversation_id:
            filters.append(
                MetadataFilter(
                    key="app_conversation_id",
                    operator=FilterOperator.EQ,
                    value=filter.conversation_id,
                ),
            )

        return VectorIndexRetriever(
            index=self.get_index(organization_id),
            similarity_top_k=top_k,
            filters=MetadataFilters(filters=filters),
            vector_store_query_mode=VectorStoreQueryMode.HYBRID,
            alpha=alpha,  # 0 means only keyword search, 1 means only semantic search
        )

    def get_node_retriever(
        self,
        nodes: list[TextNode],
    ) -> NodeRetriever:
        nodes = [NodeWithScore(node=node, score=1.0) for node in nodes]
        return NodeRetriever(nodes=nodes)

    def get_web_search_retriever(self, query: str) -> NodeRetriever:
        tavily_tool = TavilyToolSpec(api_key=os.environ.get("TAVILY_API_KEY"))
        results = tavily_tool.search(query)
        nodes = [
            NodeWithScore(
                node=TextNode(text=result.text),
                score=1.0,
            )
            for result in results
        ]
        return NodeRetriever(nodes=nodes)
