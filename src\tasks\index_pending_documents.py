import asyncio
import gc
import time

from pymongo.errors import PyMongoError

from src.models.ingestion import ExtractMetadataInput
from src.models.task import Task, TaskError, TaskPriority, TaskStatus, TaskType
from src.repositories.document import DocumentRepository
from src.repositories.project import ProjectRepository
from src.repositories.task import TaskRepository
from src.services.ingestion import IngestionService
from src.services.spaces import SpacesService
from src.util.logger import Logger
from src.util.mongo import client

task_repository = TaskRepository()
document_repository = DocumentRepository()
project_repository = ProjectRepository()
spaces_service = SpacesService()
ingestion_service = IngestionService()


async def index_pending_high_priority_documents():
    logger = Logger("IndexPendingDocumentsTask")

    try:
        tasks = await task_repository.get_priority_tasks(
            status=TaskStatus.PENDING,
            type=TaskType.DOCUMENT,
            priority=TaskPriority.HIGH,
        )

        if not tasks:
            return

        logger.info(f"Starting to index {len(tasks)} documents")

        await asyncio.gather(*[index_document(task) for task in tasks])
    except Exception as e:
        logger.error("Error indexing pending documents", e)

    return


async def index_pending_low_priority_documents():
    logger = Logger("IndexPendingDocumentsTask")

    try:
        tasks = await task_repository.get_priority_tasks(
            status=TaskStatus.PENDING,
            type=TaskType.DOCUMENT,
        )

        if not tasks:
            return

        logger.info(f"Starting to index {len(tasks)} documents")

        await asyncio.gather(*[index_document(task) for task in tasks])
    except Exception as e:
        logger.error("Error indexing pending documents", e)

    return


async def index_document(task: Task) -> None:
    logger = Logger("IndexDocumentTask")
    start_time = time.time()

    try:
        async with await client.start_session() as session:
            async with session.start_transaction():
                try:
                    await task_repository.update_one(
                        task_id=task.id,
                        session=session,
                        status=TaskStatus.IN_PROGRESS,
                    )
                    await session.commit_transaction()
                except Exception as e:
                    await session.abort_transaction()
                    return

            async with session.start_transaction():
                try:
                    await task_repository.update_one(
                        task_id=task.id,
                        session=session,
                        status=TaskStatus.PROCESSING,
                    )
                    await session.commit_transaction()
                except Exception:
                    await session.abort_transaction()
                    return

        document = await document_repository.find_one(document_id=task.document_id)
        if not document:
            error_message = f"Document {task.document_id} not found"
            logger.error(error_message)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.DOCUMENT_NOT_FOUND,
                error_message=error_message,
                duration=time.time() - start_time,
            )
            return

        project = await project_repository.find_one(project_id=document.project_id)
        if not project:
            error_message = f"Project {document.project_id} not found"
            logger.error(error_message)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.PROJECT_NOT_FOUND,
                error_message=error_message,
                duration=time.time() - start_time,
            )
            return

        download_time = time.time()
        content = None
        try:
            logger.info(f"Downloading document {document.id}")
            content = await spaces_service.download_file(path=document.path)
        except Exception as e:
            error_message = f"Error downloading file {document.path}"
            logger.error(error_message, e)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.CANNOT_DOWNLOAD,
                error_message=f"{error_message} - {str(e)}",
                duration=time.time() - start_time,
            )
            return

        if not content:
            error_message = f"Failed to download document {document.id}"
            logger.error(error_message)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.NO_CONTENT,
                error_message=error_message,
                duration=time.time() - start_time,
            )
            return

        logger.info(
            f"Downloaded document {document.id} in {time.time() - download_time} seconds"
        )

        parse_time = time.time()
        parsed_documents = None
        try:
            logger.info(f"Parsing document {document.id}")
            try:
                parsed_documents = await ingestion_service.parse(
                    content=content,
                    file_name=document.slug,
                )
            except Exception:
                logger.info(f"Retrying to parse document {document.id} with fast mode")
                parsed_documents = await ingestion_service.parse(
                    content=content,
                    file_name=document.slug,
                    fast_mode=True,
                )
        except Exception as e:
            error_message = f"Error parsing document {document.id}"
            logger.error(error_message, e)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.CANNOT_PARSE,
                error_message=f"{error_message} - {str(e)}",
                duration=time.time() - start_time,
            )
            return

        del content
        gc.collect()

        if not parsed_documents:
            error_message = f"Failed to parse document {document.id}"
            logger.error(error_message)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.NO_PARSED_CONTENT,
                error_message=error_message,
                duration=time.time() - start_time,
            )
            return

        logger.info(
            f"Parsed document {document.id} in {time.time() - parse_time} seconds"
        )

        index_time = time.time()
        result = None
        try:
            logger.info(f"Indexing document {document.id}")
            tasks = []
            for parsed_document in parsed_documents:
                tasks.append(
                    ingestion_service.ingest(
                        parsed_documents=[parsed_document],
                        input=ExtractMetadataInput(
                            organization_id=project.organization_id,
                            project_id=document.project_id,
                            user_id=document.user_id,
                            conversation_id=document.conversation_id,
                            document_id=document.id,
                            external_id=document.external_id,
                            metadata=document.metadata,
                            file_name=document.name,
                            file_slug=document.slug,
                        ),
                    )
                )

            result = await asyncio.gather(*tasks)
        except Exception as e:
            error_message = f"Error indexing document {document.id}"
            logger.error(error_message, e)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.CANNOT_INDEX,
                error_message=f"{error_message} - {str(e)}",
                duration=time.time() - start_time,
            )
            return

        del parsed_documents
        gc.collect()

        if not result:
            error_message = f"Failed to index document {document.id}"
            logger.error(error_message)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.NO_EMBEDDINGS,
                error_message=error_message,
                duration=time.time() - start_time,
            )
            return

        logger.info(
            f"Indexed document {document.id} in {time.time() - index_time} seconds"
        )

        del result
        gc.collect()

        duration = time.time() - start_time
        logger.info(f"Document {document.id} ready in {duration} seconds total")
        await asyncio.sleep(5)
        await task_repository.update_one(
            task_id=task.id,
            status=TaskStatus.DONE,
            duration=duration,
            delete_error=True,
        )
    except PyMongoError:
        return
    except Exception as e:
        duration = time.time() - start_time
        logger.error("Error indexing document", e)
        await task_repository.update_one(
            task_id=task.id,
            status=TaskStatus.FAILED,
            error=TaskError.ERROR,
            error_message=str(e),
            duration=duration,
        )
