import uuid
import json

import sentry_sdk
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware


class RequestLogMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        request_id = str(uuid.uuid4())
        body = None

        try:
            response_body = await request.body()
            if response_body:
                body = json.loads(response_body.decode("utf-8"))
        except Exception:
            pass

        sentry_sdk.add_breadcrumb(
            category="Request",
            message=f"Request {request_id}: {request.method} {request.url}",
            data={
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "headers": dict(request.headers),
                "body": body,
                "token": request.headers.get("Authorization", None),
                "api-key": request.headers.get("X-API-Key", None),
            },
            level="info",
        )

        response = await call_next(request)

        sentry_sdk.add_breadcrumb(
            category="Response",
            message=f"Response {request_id}: {request.method} {request.url}",
            data={
                "request_id": request_id,
                "status_code": response.status_code,
                "method": request.method,
                "url": str(request.url),
                "headers": dict(response.headers),
                "body": body,
                "token": request.headers.get("Authorization", None),
                "api-key": request.headers.get("X-API-Key", None),
            },
            level="info",
        )

        return response
