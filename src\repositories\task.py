from datetime import datetime

from bson import ObjectId
from dateutil.parser import isoparse
from pymongo import DESCENDING, ReturnDocument
from pymongo.client_session import ClientSession
from pytz import utc

from src.models.task import (
    InsertTaskInput,
    Task,
    TaskAction,
    TaskError,
    TaskPriority,
    TaskStatus,
    TaskType,
)
from src.util.mongo import db


class TaskRepository:
    def __init__(self):
        self.collection = db.get_collection("tasks")

    def _to_model(self, task_dict: dict) -> Task:
        document_id = task_dict.get("document_id")
        if document_id:
            document_id = str(document_id)
        context_id = task_dict.get("context_id")
        if context_id:
            context_id = str(context_id)
        return Task(
            id=str(task_dict.get("_id")),
            status=task_dict.get("status"),
            priority=task_dict.get("priority"),
            action=task_dict.get("action"),
            duration=task_dict.get("duration"),
            error=task_dict.get("error"),
            error_message=task_dict.get("error_message"),
            document_id=document_id,
            context_id=context_id,
            reset_count=task_dict.get("reset_count"),
            created_at=task_dict.get("created_at"),
            updated_at=task_dict.get("updated_at"),
        )

    async def find_last_one(
        self,
        task_id: str | None = None,
        document_id: str | None = None,
        context_id: str | None = None,
        status: TaskStatus | None = None,
        priority: TaskPriority | None = None,
        action: TaskAction | None = None,
    ) -> Task | None:
        filter = {}
        if task_id:
            filter["_id"] = ObjectId(task_id)
        if document_id:
            filter["document_id"] = ObjectId(document_id)
        if context_id:
            filter["context_id"] = ObjectId(context_id)
        if status:
            filter["status"] = status
        if priority:
            filter["priority"] = priority
        if action:
            filter["action"] = action
        task_dict = await self.collection.find_one(
            filter=filter,
            sort=[("created_at", DESCENDING)],
        )
        return self._to_model(task_dict) if task_dict else None

    async def find_many(
        self,
        task_ids: list[str] = [],
        document_ids: list[str] = [],
        context_ids: list[str] = [],
        status: TaskStatus | None = None,
        priority: TaskPriority | None = None,
        action: TaskAction | None = None,
        error: TaskError | None = None,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[Task]:
        filter = {}
        if task_ids:
            filter["_id"] = {"$in": [ObjectId(task_id) for task_id in task_ids]}
        if document_ids:
            filter["document_id"] = {
                "$in": [ObjectId(document_id) for document_id in document_ids]
            }
        if context_ids:
            filter["context_id"] = {
                "$in": [ObjectId(context_id) for context_id in context_ids]
            }
        if status:
            filter["status"] = status
        if priority:
            filter["priority"] = priority
        if action:
            filter["action"] = action
        if error:
            filter["error"] = error
        tasks = await self.collection.find(filter=filter, sort=sort).to_list()
        return [self._to_model(task) for task in tasks]

    async def insert_one(self, input: InsertTaskInput) -> Task | None:
        result = await self.collection.insert_one(input.to_dict())
        return await self.find_last_one(task_id=result.inserted_id)

    async def insert_many(
        self,
        inputs: list[InsertTaskInput],
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[Task]:
        task_dicts = [input.to_dict() for input in inputs]
        result = await self.collection.insert_many(task_dicts)
        return await self.find_many(task_ids=result.inserted_ids, sort=sort)

    async def delete_one(
        self,
        task_id: str | None = None,
        document_id: str | None = None,
        context_id: str | None = None,
    ) -> Task | None:
        task = await self.find_one(
            task_id=task_id,
            document_id=document_id,
            context_id=context_id,
        )
        if not task:
            return None
        filter = {}
        if task_id:
            filter["_id"] = ObjectId(task_id)
        if document_id:
            filter["document_id"] = ObjectId(document_id)
        if context_id:
            filter["context_id"] = ObjectId(context_id)
        await self.collection.delete_one(filter=filter)
        return task

    async def delete_many(
        self,
        task_ids: list[str] = [],
        document_ids: list[str] = [],
        context_ids: list[str] = [],
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[Task]:
        tasks = await self.find_many(
            task_ids=task_ids,
            document_ids=document_ids,
            context_ids=context_ids,
            sort=sort,
        )
        filter = {}
        if task_ids:
            filter["_id"] = {"$in": [ObjectId(task_id) for task_id in task_ids]}
        if document_ids:
            filter["document_id"] = {
                "$in": [ObjectId(document_id) for document_id in document_ids]
            }
        if context_ids:
            filter["context_id"] = {
                "$in": [ObjectId(context_id) for context_id in context_ids]
            }
        await self.collection.delete_many(filter=filter)
        return tasks

    async def get_priority_tasks(
        self,
        status: TaskStatus,
        priority: TaskPriority = None,
        type: TaskType = None,
        limit: int = 10,
    ) -> list[Task]:
        filter = {"status": status}
        if type == TaskType.CONTEXT:
            filter["context_id"] = {"$ne": None}
        elif type == TaskType.DOCUMENT:
            filter["document_id"] = {"$ne": None}
        if priority:
            filter["priority"] = priority
        task_dicts = await self.collection.find(
            filter=filter,
            limit=limit,
            sort=[("priority", DESCENDING), ("created_at", DESCENDING)],
        ).to_list()
        return [self._to_model(task) for task in task_dicts]

    async def update_one(
        self,
        task_id: str,
        session: ClientSession | None = None,
        status: TaskStatus | None = None,
        error: TaskError | None = None,
        error_message: str = None,
        duration: float = None,
        delete_error: bool = False,
    ) -> Task | None:
        update = {"updated_at": datetime.now(utc)}
        if status:
            update["status"] = status
        if error:
            update["error"] = error
        if error_message:
            update["error_message"] = error_message
        if duration:
            update["duration"] = duration
        if delete_error:
            update["error"] = None
            update["error_message"] = None
        task_dict = await self.collection.find_one_and_update(
            filter={"_id": ObjectId(task_id)},
            update={"$set": update},
            session=session,
            return_document=ReturnDocument.AFTER,
        )
        return self._to_model(task_dict) if task_dict else None

    async def reset_status(
        self,
        status: TaskStatus,
        max_retries: int = 1,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
    ) -> None:
        date_filter = {}
        if start_date:
            date_filter["$gte"] = start_date
        if end_date:
            date_filter["$lte"] = end_date

        filter = {"reset_count": {"$lt": max_retries}, "status": status}
        if date_filter:
            filter["updated_at"] = date_filter

        await self.collection.update_many(
            filter=filter,
            update={
                "$set": {
                    "status": TaskStatus.PENDING,
                    "updated_at": datetime.now(utc),
                },
                "$inc": {"reset_count": 1},
            },
        )
