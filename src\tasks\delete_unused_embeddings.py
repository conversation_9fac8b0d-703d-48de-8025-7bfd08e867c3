import asyncio

from bson import ObjectId

from src.services.pinecone import PineconeService
from src.util.logger import Logger
from src.util.mongo import db

pinecone_service = PineconeService()


async def delete_unused_embeddings():
    logger = Logger("DeleteUnusedEmbeddingsTask")
    organizations_collection = db["organizations"]
    documents_collection = db["documents"]
    contexts_collection = db["contexts"]

    try:
        organizations = await organizations_collection.find().to_list()

        logger.info(
            f"Deleting unused embeddings for {len(organizations)} organizations"
        )

        for organization in organizations:
            try:
                logger.info(
                    f"Deleting unused embeddings for organization {organization['_id']}"
                )
                organization_id = str(organization["_id"])
                first_iteration = True
                document_ids_to_delete = []
                context_ids_to_delete = []
                vector_ids_to_delete = []
                pagination_token = None

                while pagination_token or first_iteration:
                    first_iteration = False
                    vector_list = await pinecone_service.list_ids(
                        organization_id=organization_id,
                        pagination_token=pagination_token,
                    )
                    vector_ids = [vector["id"] for vector in vector_list["vectors"]]
                    if not vector_ids:
                        break
                    pagination = vector_list.get("pagination", None)
                    if pagination:
                        pagination_token = pagination.get("next", None)
                    else:
                        pagination_token = None

                    vectors = await pinecone_service.fetch_by_ids(
                        organization_id=organization_id,
                        ids=vector_ids,
                    )

                    for vector in vectors.values():
                        metadata = vector.get("metadata", {})

                        document_id = metadata.get("app_document_id")
                        if document_id:
                            if document_id in document_ids_to_delete:
                                vector_ids_to_delete.append(vector["id"])
                            else:
                                document = await documents_collection.find_one(
                                    {"_id": ObjectId(document_id)}
                                )
                                if not document:
                                    document_ids_to_delete.append(document_id)
                                    vector_ids_to_delete.append(vector["id"])

                        context_id = metadata.get("app_context_id")
                        if context_id:
                            if context_id in context_ids_to_delete:
                                vector_ids_to_delete.append(vector["id"])
                            else:
                                context = await contexts_collection.find_one(
                                    {"_id": ObjectId(context_id)}
                                )
                                if not context:
                                    context_ids_to_delete.append(context_id)
                                    vector_ids_to_delete.append(vector["id"])

                if vector_ids_to_delete:
                    logger.info(
                        f"Deleting {len(vector_ids_to_delete)} vectors for organization {organization_id}"
                    )
                    await pinecone_service.delete_by_ids(
                        organization_id=organization_id,
                        ids=vector_ids_to_delete,
                    )
            except Exception as e:
                logger.error(
                    f"Error deleting unused embeddings for organization {organization_id}",
                    e,
                )
    except Exception as e:
        logger.error("Error deleting unused embeddings", e)
