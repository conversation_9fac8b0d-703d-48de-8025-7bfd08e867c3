from datetime import datetime

from fastapi import HTTPException
from pymongo import ASCENDING
from pytz import utc

from src.models.auth import Auth
from src.models.document import DocumentTask
from src.models.export import ExportResponse, InsertExportInput
from src.models.shared import to_iso_8601
from src.repositories.document import DocumentRepository
from src.repositories.export import ExportRepository
from src.repositories.message import MessageRepository
from src.repositories.task import TaskRepository
from src.services.spaces import SpacesService
from src.util.logger import Logger
from src.util.mongo import db


class ExportService:
    def __init__(self):
        self.collection = db.get_collection("exports")
        self.spaces_service = SpacesService()
        self.logger = Logger("ExportService")
        self.export_repository = ExportRepository()
        self.message_repository = MessageRepository()
        self.document_repository = DocumentRepository()
        self.task_repository = TaskRepository()

    async def export_user_queries(
        self,
        auth: Auth,
        user_id: str | None = None,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
    ) -> ExportResponse:
        try:
            if not auth.has_permission():
                raise HTTPException(
                    "You don't have permission to export this user's messages", 403
                )
            user_queries = await self.message_repository.find_many_user_queries(
                project_id=auth.project.id,
                user_id=auth.get_member_user_id(user_id),
                start_date=start_date,
                end_date=end_date,
            )
            csv = "user_id,conversation_id,message,date,time\n"
            for user_query in user_queries:
                date = user_query.created_at.strftime("%x")
                time = user_query.created_at.strftime("%X")
                csv += f'{user_query.user_id},{user_query.conversation_id},"{user_query.text}",{date},{time}\n'
            content = csv.encode()
            path = f"exports/{auth.organization.id}/{auth.project.id}/messages/{to_iso_8601(datetime.now(utc))}.csv"
            uploaded = await self.spaces_service.upload_file(content, path, "text/csv")
            if not uploaded:
                raise HTTPException("Error uploading user queries export", 500)
            url = self.spaces_service.get_file_url(path)
            export = await self.export_repository.insert_one(
                input=InsertExportInput(
                    user_id=auth.user.id,
                    url=url,
                )
            )
            return ExportResponse(
                id=export.id,
                user_id=export.user_id,
                url=export.url,
                created_at=export.created_at,
            )
        except Exception as e:
            self.logger.error("Error exporting user input report", e)
            raise HTTPException(
                status_code=500,
                detail="Error exporting user input report",
            )

    async def export_file_uploads(
        self,
        auth: Auth,
        user_id: str | None = None,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
    ) -> ExportResponse:
        try:
            if not auth.has_permission():
                raise HTTPException(
                    "You don't have permission to export this user's file uploads", 403
                )
            documents = await self.document_repository.find_many(
                project_id=auth.project.id,
                user_id=auth.get_member_user_id(user_id),
                start_date=start_date,
                end_date=end_date,
            )
            document_ids = [document.id for document in documents]
            tasks = await self.task_repository.find_many(
                document_ids=document_ids,
            )
            document_tasks = []
            for document in documents:
                document_tasks.append(
                    DocumentTask(
                        document=document,
                        task=next(
                            (task for task in tasks if task.document_id == document.id),
                            None,
                        ),
                    )
                )
            csv = "document_id,name,content_type,size (MB),metadata,keywords,date,time,external_id,user_id,task_id,status,priority,duration (seconds),error,error_message,reset_count\n"
            for document_task in document_tasks:
                document = document_task.document
                task = document_task.task
                date = document.created_at.strftime("%x")
                time = document.created_at.strftime("%X")
                size = f"{document.size / 1024 / 1024:.2f}"
                metadata = document.metadata or "null"
                keywords = document.keywords.to_dict() if document.keywords else "null"
                external_id = document.external_id or "null"
                user_id = document.user_id or "null"
                task_id = task.id if task else "null"
                status = task.status.value if task else "null"
                priority = task.priority.value if task else "null"
                duration = f"{task.duration:.2f}" if task else "null"
                error = task.error.value if task and task.error else "null"
                error_message = task.error_message if task and task.error else "null"
                reset_count = task.reset_count if task else "null"
                csv += f'{document.id},{document.name},{document.content_type},{size},"{metadata}","{keywords}",{date},{time},{external_id},{user_id},{task_id},{status},{priority},{duration},{error},{error_message},{reset_count}\n'
            content = csv.encode()
            path = f"exports/{auth.organization.id}/{auth.project.id}/file_uploads/{to_iso_8601(datetime.now(utc))}.csv"
            uploaded = await self.spaces_service.upload_file(content, path, "text/csv")
            if not uploaded:
                raise HTTPException("Error uploading file uploads export", 500)
            url = self.spaces_service.get_file_url(path)
            export = await self.export_repository.insert_one(
                input=InsertExportInput(
                    user_id=auth.user.id,
                    url=url,
                )
            )
            return ExportResponse(
                id=export.id,
                user_id=export.user_id,
                url=export.url,
                created_at=export.created_at,
            )
        except Exception as e:
            self.logger.error("Error exporting file uploads report", e)
            raise HTTPException(
                status_code=500,
                detail="Error exporting file uploads report",
            )
