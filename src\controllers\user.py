from fastapi import (
    APIRouter,
    Request,
)

from src.models.user import Patch<PERSON><PERSON><PERSON><PERSON>, UserResponse
from src.services.user import UserService

user_router = APIRouter()
user_service = UserService()


@user_router.get("/me", response_model=UserResponse)
async def find_one(request: Request):
    return user_service.find_current_user(auth=request.state.auth)


@user_router.patch("", response_model=UserResponse)
async def patch_one(request: Request, body: PatchUserBody):
    return await user_service.patch_one(auth=request.state.auth, body=body)
