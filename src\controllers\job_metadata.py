from fastapi import APIRouter, Request

from src.models.job_metadata import (
    CreateJobMetadataBody,
    DeleteJobMetadataBody,
    JobMetadataResponse,
    JobType,
    UpdateJobMetadataBody,
)
from src.models.shared import DeleteResponse, PaginationResponse
from src.services.job_metadata import JobMetadataService
from src.util.scheduler import scheduler  # Import the shared scheduler instance

job_metadata_router = APIRouter()
job_metadata_service = JobMetadataService()


@job_metadata_router.post("", response_model=JobMetadataResponse)
async def post_one(request: Request, body: CreateJobMetadataBody):
    return await job_metadata_service.post_one(
        auth=request.state.auth,
        body=body,
    )


@job_metadata_router.get(
    "/paginate", response_model=PaginationResponse[JobMetadataResponse]
)
async def paginate(
    request: Request,
    type: JobType | None = None,
    name: str | None = None,
    page_size: int = 10,
    page_number: int = 1,
):
    return await job_metadata_service.paginate(
        auth=request.state.auth,
        type=type,
        name=name,
        page_size=page_size,
        page_number=page_number,
    )


@job_metadata_router.get("/{job_metadata_id}", response_model=JobMetadataResponse)
async def get_one(request: Request, job_metadata_id: str):
    return await job_metadata_service.get_one(
        auth=request.state.auth,
        job_metadata_id=job_metadata_id,
    )


@job_metadata_router.get("", response_model=list[JobMetadataResponse])
async def get_many(
    request: Request,
    type: JobType | None = None,
):
    return await job_metadata_service.get_many(
        auth=request.state.auth,
        type=type,
    )


@job_metadata_router.patch("/{job_metadata_id}", response_model=JobMetadataResponse)
async def patch_one(
    request: Request, job_metadata_id: str, body: UpdateJobMetadataBody
):
    return await job_metadata_service.update_one(
        auth=request.state.auth,
        job_metadata_id=job_metadata_id,
        body=body,
    )


@job_metadata_router.delete("/{job_metadata_id}", response_model=JobMetadataResponse)
async def delete_one(request: Request, job_metadata_id: str):
    return await job_metadata_service.delete_one(
        auth=request.state.auth,
        job_metadata_id=job_metadata_id,
    )


@job_metadata_router.delete("", response_model=DeleteResponse)
async def delete_many(request: Request, body: DeleteJobMetadataBody):
    return await job_metadata_service.delete_many(
        auth=request.state.auth,
        body=body,
    )


@job_metadata_router.get("/status")
async def get_scheduler_status(request: Request):
    """Get the current status of the scheduler and its jobs"""
    # Only allow admin users to access this endpoint
    if not request.state.auth.user.is_admin:
        return {"error": "Unauthorized"}

    # Get all jobs from the scheduler
    jobs = scheduler.get_jobs()

    # Format job information for response
    job_info = []
    for job in jobs:
        job_info.append(
            {
                "id": job.id,
                "name": job.name,
                "next_run_time": (
                    job.next_run_time.isoformat() if job.next_run_time else None
                ),
                "trigger": str(job.trigger),
                "misfire_grace_time": job.misfire_grace_time,
                "max_instances": job.max_instances,
                "coalesce": job.coalesce,
            }
        )

    # Return scheduler status information
    return {
        "scheduler_running": scheduler.running,
        "job_count": len(jobs),
        "jobs": job_info,
    }
