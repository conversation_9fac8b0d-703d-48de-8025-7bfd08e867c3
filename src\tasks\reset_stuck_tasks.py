import asyncio
from datetime import datetime, timedelta

from pytz import utc

from src.models.task import TaskStatus
from src.repositories.task import TaskRepository
from src.util.logger import Logger

task_repository = TaskRepository()


async def reset_stuck_tasks():
    logger = Logger("ResetStuckTasksTask")

    try:
        logger.info("Resetting stuck tasks")

        await asyncio.gather(
            task_repository.reset_status(
                status=TaskStatus.IN_PROGRESS,
                end_date=datetime.now(utc) - timedelta(minutes=2),
            ),
            task_repository.reset_status(
                status=TaskStatus.PROCESSING,
                end_date=datetime.now(utc) - timedelta(minutes=30),
            ),
            task_repository.reset_status(status=TaskStatus.FAILED),
        )
    except Exception as e:
        logger.error("Error resetting stuck tasks", e)
