from llama_index.llms.anthropic import Anthropic
from llama_index.llms.gemini import Gemini
from llama_index.llms.groq import <PERSON><PERSON>q
from llama_index.llms.openai import OpenAI

from src.models.auth import Auth
from src.models.llm import LLMProvider
from src.models.settings import (
    InsertSettingsInput,
    Settings,
    SettingsResponse,
    UpdateSettingsBody,
    UpdateSettingsInput,
)
from src.rag.config import OPEN_AI_LLM
from src.rag.grok_llm import Grok
from src.repositories.settings import SettingsRepository
from src.util.llm_validation import validate_provider_model_compatibility
from src.util.logger import Logger


class SettingsService:
    def __init__(self):
        self.logger = Logger("SettingsService")
        self.settings_repository = SettingsRepository()

    async def get_llm(
        self, project_id: str, user_id: str
    ) -> OpenAI | Gemini | Groq | Anthropic | Grok:
        settings = await self.settings_repository.find_one(
            project_id=project_id,
            user_id=user_id,
        )

        provider, model = validate_provider_model_compatibility(
            settings.llm_provider, settings.llm_model
        )

        if provider == LLMProvider.OPENAI:
            return OpenAI(model=model.value, temperature=0.4, max_retries=10)
        elif provider == LLMProvider.GEMINI:
            return Gemini(model=model.value, temperature=1.0, max_tokens=8192)
        elif provider == LLMProvider.GROQ:
            return Groq(model=model.value)
        elif provider == LLMProvider.CLAUDE:
            return Anthropic(model=model.value)
        elif provider == LLMProvider.GROK:
            return Grok(model=model.value, temperature=0.4, max_tokens=8192)
        else:
            return OPEN_AI_LLM

    async def get_one(self, auth: Auth) -> SettingsResponse | None:
        settings = await self.settings_repository.find_one(
            project_id=auth.project.id,
            user_id=auth.user.id,
        )
        return self._to_settings_response(settings) if settings else None

    async def update_one(
        self,
        auth: Auth,
        body: UpdateSettingsBody,
    ) -> SettingsResponse:
        existing_settings = await self.settings_repository.find_one(
            project_id=auth.project.id,
            user_id=auth.user.id,
        )

        provider, model = validate_provider_model_compatibility(
            body.llm_provider, body.llm_model
        )

        if existing_settings:
            settings = await self.settings_repository.update_one(
                settings_id=existing_settings.id,
                input=UpdateSettingsInput(
                    llm_provider=provider,
                    llm_model=model,
                ),
            )
        else:
            settings = await self.settings_repository.insert_one(
                InsertSettingsInput(
                    project_id=auth.project.id,
                    user_id=auth.user.id,
                    llm_provider=provider,
                    llm_model=model,
                )
            )

        return self._to_settings_response(settings)

    def _to_settings_response(
        self,
        settings: Settings,
    ) -> SettingsResponse:
        provider, model = validate_provider_model_compatibility(
            settings.llm_provider, settings.llm_model
        )

        return SettingsResponse(
            id=settings.id,
            llm_provider=provider,
            llm_model=model,
            created_at=settings.created_at,
        )
