from datetime import datetime
from enum import Enum

from src.models.shared import ConfigModel


class JobType(str, Enum):
    UPLOADED_DOCUMENTS_SUMMARY = "uploaded_documents_summary"


class PeriodUnit(str, Enum):
    NOW = "now"
    SECONDS = "seconds"
    MINUTES = "minutes"
    HOURS = "hours"
    DAYS = "days"
    WEEKS = "weeks"


class JobMetadata(ConfigModel):
    id: str
    job_id: str
    project_id: str
    user_id: str
    name: str
    period_unit: PeriodUnit
    period_value: int
    metadata: dict = {}
    created_at: datetime
    type: JobType | None = None


class JobMetadataResponse(ConfigModel):
    id: str
    user_id: str
    name: str
    period_unit: PeriodUnit
    period_value: int
    created_at: datetime
    period_unit: PeriodUnit
    period_value: int
    metadata: dict = {}
    type: JobType | None = None
    next_run: datetime | None = None


class CreateJobMetadataBody(ConfigModel):
    name: str
    period_unit: PeriodUnit = PeriodUnit.NOW
    period_value: int = 1
    metadata: dict = {}
    type: JobType | None = None


class UpdateJobMetadataBody(ConfigModel):
    name: str | None = None
    period_unit: PeriodUnit | None = None
    period_value: int | None = None
    metadata: dict | None = None
    type: JobType | None = None


class DeleteJobMetadataBody(ConfigModel):
    job_metadata_ids: list[str] = []


VALID_PERIODS_PER_JOB_TYPE = {
    JobType.UPLOADED_DOCUMENTS_SUMMARY: [
        PeriodUnit.NOW,
        PeriodUnit.DAYS,
        PeriodUnit.WEEKS,
    ],
}
