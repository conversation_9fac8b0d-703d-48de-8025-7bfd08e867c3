import os

import aioboto3
import botocore

from src.util.logger import Logger

bucket = os.getenv("DO_SPACES_BUCKET")


class SpacesService:
    def __init__(self):
        self.session = aioboto3.Session()
        self.logger = Logger("SpacesService")

    def get_file_url(self, path: str):
        return f"https://{bucket}.sfo2.digitaloceanspaces.com/{path}"

    async def list_files(self) -> list[str]:
        try:
            async with self.session.client(
                service_name="s3",
                region_name="sfo2",
                endpoint_url="https://sfo2.digitaloceanspaces.com",
                config=botocore.config.Config(s3={"addressing_style": "virtual"}),
                aws_access_key_id=os.getenv("DO_SPACES_ACCESS_KEY"),
                aws_secret_access_key=os.getenv("DO_SPACES_SECRET_KEY"),
            ) as client:
                files = await client.list_objects_v2(Bucket=bucket)
                return [file.get("Key") for file in files.get("Contents", [])]
        except Exception as e:
            self.logger.error("Failed to list files", e)
            return []

    async def upload_file(
        self, content: bytes, path: str, content_type: str = "application/pdf" 
    ) -> bool:
        try:
            async with self.session.client(
                service_name="s3",
                region_name="sfo2",
                endpoint_url="https://sfo2.digitaloceanspaces.com",
                config=botocore.config.Config(s3={"addressing_style": "virtual"}),
                aws_access_key_id=os.getenv("DO_SPACES_ACCESS_KEY"),
                aws_secret_access_key=os.getenv("DO_SPACES_SECRET_KEY"),
            ) as client:
                await client.put_object(
                    Bucket=bucket,
                    Key=path,
                    Body=content,
                    ACL="public-read",
                    ContentType=content_type,
                )
                return True
        except Exception as e:
            self.logger.error(f"Failed to upload file: {path}", e)
            return False

    async def download_file(self, path: str) -> bytes | None:
        try:
            async with self.session.client(
                service_name="s3",
                region_name="sfo2",
                endpoint_url="https://sfo2.digitaloceanspaces.com",
                config=botocore.config.Config(s3={"addressing_style": "virtual"}),
                aws_access_key_id=os.getenv("DO_SPACES_ACCESS_KEY"),
                aws_secret_access_key=os.getenv("DO_SPACES_SECRET_KEY"),
            ) as client:
                file = await client.get_object(Bucket=bucket, Key=path)
                return await file["Body"].read()
        except Exception as e:
            self.logger.error(f"Failed to download file: {path}", e)
            return None

    async def delete_file(self, path: str) -> bool:
        try:
            async with self.session.client(
                service_name="s3",
                region_name="sfo2",
                endpoint_url="https://sfo2.digitaloceanspaces.com",
                config=botocore.config.Config(s3={"addressing_style": "virtual"}),
                aws_access_key_id=os.getenv("DO_SPACES_ACCESS_KEY"),
                aws_secret_access_key=os.getenv("DO_SPACES_SECRET_KEY"),
            ) as client:
                await client.delete_object(Bucket=bucket, Key=path)
                return True
        except Exception as e:
            self.logger.error(f"Failed to delete file: {path}", e)
            return False
