import asyncio

from fastapi import HTTPException
from llama_index.agent.openai import OpenAIAgent
from llama_index.core.llms import ChatMessage

from src.models.auth import Auth
from src.models.conversation import (
    DEFAULT_CONVERSATION_TITLE,
    Conversation,
    ConversationResponse,
    ConversationUsageResponse,
    CreateConversationBody,
    DeleteConversationsBody,
    InsertConversationInput,
    UpdateConversationBody,
    UpdateConversationInput,
)
from src.models.document import DocumentResponse
from src.models.message import (
    CreateMessageBody,
    DocumentMessage,
    DocumentMessageResponse,
    InsertDocumentMessageInput,
    InsertMessageInput,
    Message,
    MessageResponse,
    MessageType,
)
from src.models.shared import DeleteResponse
from src.rag.config import GEMINI_LLM
from src.repositories.conversation import ConversationRepository
from src.repositories.document import DocumentRepository
from src.services.document import DocumentService
from src.util.llm_validation import validate_provider_model_compatibility
from src.util.logger import Logger


class ConversationService:
    def __init__(self):
        self.conversation_repository = ConversationRepository()
        self.document_repository = DocumentRepository()
        self.document_service = DocumentService()
        self.logger = Logger("ConversationService")

    async def get_one(self, auth: Auth, conversation_id: str) -> ConversationResponse:
        (conversation, document_responses) = await asyncio.gather(
            self.conversation_repository.find_one(
                project_id=auth.project.id,
                conversation_id=conversation_id,
                user_id=auth.get_user_id(),
            ),
            self.document_service.get_many(
                auth=auth,
                conversation_id=conversation_id,
            ),
        )

        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")

        return self._to_conversation_response(
            conversation=conversation,
            document_responses=document_responses,
        )

    async def get_many(
        self,
        auth: Auth,
        conversation_ids: list[str] = [],
        user_id: str | None = None,
    ) -> list[ConversationResponse]:
        conversations = await self.conversation_repository.find_many(
            project_id=auth.project.id,
            conversation_ids=conversation_ids,
            user_id=auth.get_user_id(user_id),
        )
        return [
            self._to_conversation_response(conversation)
            for conversation in conversations
        ]

    async def usage(
        self,
        auth: Auth,
        user_id: str | None = None,
    ) -> ConversationUsageResponse:
        return await self.conversation_repository.usage(
            project_id=auth.project.id,
            user_id=auth.get_member_user_id(user_id),
        )

    async def post_one(
        self,
        auth: Auth,
        body: CreateConversationBody,
    ) -> Conversation:
        await self.conversation_repository.delete_empty(user_id=auth.user.id)

        conversation = await self.conversation_repository.insert_one(
            input=InsertConversationInput(
                project_id=auth.project.id,
                title=body.title or DEFAULT_CONVERSATION_TITLE,
                user_id=auth.user.id,
            )
        )

        if not conversation:
            raise HTTPException(status_code=400, detail="Error creating conversation")

        return self._to_conversation_response(conversation=conversation)

    async def post_one_message(
        self,
        auth: Auth,
        conversation_id: str,
        body: CreateMessageBody,
    ) -> ConversationResponse:
        documents = []
        if body.uploaded_document_ids:
            documents = await self.document_repository.find_many(
                project_id=auth.project.id,
                document_ids=body.uploaded_document_ids,
            )

        (conversation, document_responses) = await asyncio.gather(
            self.conversation_repository.update_one(
                conversation_id=conversation_id,
                project_id=auth.project.id,
                user_id=auth.user.id,
                input=UpdateConversationInput(
                    add_messages=[
                        InsertMessageInput(
                            is_user=body.is_user,
                            type=body.type,
                            text=body.text,
                            documents=[
                                InsertDocumentMessageInput(
                                    document_id=document.id,
                                    name=document.name,
                                    slug=document.slug,
                                    url=document.url,
                                    size=document.size,
                                    content_type=document.content_type,
                                    source_url=document.source_url,
                                )
                                for document in documents
                            ],
                        ),
                    ]
                ),
            ),
            self.document_service.get_many(
                auth=auth,
                conversation_id=conversation_id,
            ),
        )

        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")

        if (
            conversation.title == DEFAULT_CONVERSATION_TITLE
            and len(conversation.messages) > 0
        ):
            asyncio.create_task(
                self.update_title_from_message(
                    project_id=auth.project.id,
                    conversation_id=conversation_id,
                    message=conversation.messages[0],
                )
            )

        return self._to_conversation_response(
            conversation=conversation,
            document_responses=document_responses,
        )

    async def patch_one(
        self,
        auth: Auth,
        conversation_id: str,
        body: UpdateConversationBody,
    ) -> ConversationResponse:
        (conversation, document_responses) = await asyncio.gather(
            self.conversation_repository.update_one(
                conversation_id=conversation_id,
                project_id=auth.project.id,
                user_id=auth.user.id,
                input=UpdateConversationInput(
                    title=body.title,
                ),
            ),
            self.document_service.get_many(
                auth=auth,
                conversation_id=conversation_id,
            ),
        )

        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")

        return self._to_conversation_response(
            conversation=conversation,
            document_responses=document_responses,
        )

    async def delete_many(
        self,
        auth: Auth,
        body: DeleteConversationsBody,
    ) -> DeleteResponse:
        if not auth.has_permission():
            raise HTTPException(
                status_code=403, detail="You can't delete other user's conversations"
            )
        conversations = await self.conversation_repository.delete_many(
            project_id=auth.project.id,
            user_id=auth.user.id,
            conversation_ids=body.conversation_ids,
        )
        return DeleteResponse(
            count=len(conversations),
            ids=[conversation.id for conversation in conversations],
        )

    async def generate_title_from_message(
        self,
        project_id: str,
        message: Message,
        user_id: str | None = None,
    ) -> str:
        title = DEFAULT_CONVERSATION_TITLE

        query = None
        if message.type == MessageType.TEXT and message.text:
            query = message.text
        elif message.type == MessageType.FILE and message.documents:
            documents = await self.document_repository.find_many(
                project_id=project_id,
                document_ids=[document.document_id for document in message.documents],
                user_id=user_id,
            )
            if documents:
                query = "Documents uploaded: " + ", ".join(
                    [document.name for document in documents]
                )

        if query:
            title = await self.generate_conversation_title(query)

        return title

    async def update_title_from_message(
        self,
        project_id: str,
        conversation_id: str,
        message: Message,
    ):
        title = await self.generate_title_from_message(
            project_id=project_id,
            message=message,
        )
        return await self.conversation_repository.update_one(
            conversation_id=conversation_id,
            project_id=project_id,
            input=UpdateConversationInput(title=title),
        )

    async def generate_conversation_title(self, query: str) -> str | None:
        try:
            system_prompt = f"""
                You are a helpful AI assistant. 
                You job is to generate a title based on a user query. 
                The title must be relevant to the query.
                Only answer with the title, nothing else. 
                Don't return generic titles like "Document upload confirmation" or "Chat summary".
                The title must have 40 characters maximum.
                The first word of the title must have a capital letter.
                The rest of the title must be in lowercase.
                The user query is: {query}
                Now generate the title:
            """
            llm = GEMINI_LLM
            response = await llm.acomplete(system_prompt)
            return response.text if response else None
        except Exception as e:
            self.logger.error("Error generating conversation title", e)
            return None

    def _to_conversation_response(
        self,
        conversation: Conversation,
        document_responses: list[DocumentResponse] = [],
    ) -> ConversationResponse:
        return ConversationResponse(
            id=conversation.id,
            title=conversation.title,
            scores=conversation.scores,
            documents=document_responses,
            user_id=conversation.user_id,
            created_at=conversation.created_at,
            messages=[
                self._to_message_response(message) for message in conversation.messages
            ],
        )

    def _to_message_response(self, message: Message) -> MessageResponse:
        provider, model = validate_provider_model_compatibility(
            message.llm_provider, message.llm_model
        )

        return MessageResponse(
            id=message.id,
            is_user=message.is_user,
            type=message.type,
            text=message.text,
            created_at=message.created_at,
            documents=[
                self._to_document_message_response(document)
                for document in message.documents
            ],
            context_id=message.context_id,
            llm_provider=provider,
            llm_model=model,
        )

    def _to_document_message_response(
        self, document_message: DocumentMessage
    ) -> DocumentMessageResponse:
        return DocumentMessageResponse(
            document_id=document_message.document_id,
            name=document_message.name,
            slug=document_message.slug,
            url=document_message.url,
            size=document_message.size,
            content_type=document_message.content_type,
            source_url=document_message.source_url,
        )
