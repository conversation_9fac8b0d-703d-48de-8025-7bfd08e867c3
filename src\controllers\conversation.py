from fastapi import APIRouter, Query, Request

from src.models.conversation import (
    ConversationResponse,
    ConversationUsageResponse,
    CreateConversationBody,
    DeleteConversationsBody,
    UpdateConversationBody,
)
from src.models.message import CreateMessageBody
from src.models.shared import DeleteResponse
from src.services.conversation import ConversationService

conversation_router = APIRouter()
conversation_service = ConversationService()


@conversation_router.get("/usage", response_model=ConversationUsageResponse)
async def usage(request: Request, user_id: str | None = None):
    return await conversation_service.usage(
        auth=request.state.auth,
        user_id=user_id,
    )


@conversation_router.get("/{conversation_id}", response_model=ConversationResponse)
async def get_one(request: Request, conversation_id: str):
    return await conversation_service.get_one(
        auth=request.state.auth,
        conversation_id=conversation_id,
    )


@conversation_router.get("", response_model=list[ConversationResponse])
async def get_many(
    request: Request,
    conversation_ids: list[str] | None = Query(None),
    user_id: str | None = None,
):
    return await conversation_service.get_many(
        auth=request.state.auth,
        conversation_ids=conversation_ids,
        user_id=user_id,
    )


@conversation_router.post("", response_model=ConversationResponse)
async def post_one(request: Request, body: CreateConversationBody):
    return await conversation_service.post_one(
        auth=request.state.auth,
        body=body,
    )


@conversation_router.post(
    "/{conversation_id}/messages", response_model=ConversationResponse
)
async def post_one_message(
    request: Request,
    conversation_id: str,
    body: CreateMessageBody,
):
    return await conversation_service.post_one_message(
        auth=request.state.auth,
        conversation_id=conversation_id,
        body=body,
    )


@conversation_router.patch("/{conversation_id}", response_model=ConversationResponse)
async def patch_one(
    request: Request,
    conversation_id: str,
    body: UpdateConversationBody,
):
    return await conversation_service.patch_one(
        auth=request.state.auth,
        conversation_id=conversation_id,
        body=body,
    )


@conversation_router.delete("", response_model=DeleteResponse)
async def delete_many(request: Request, body: DeleteConversationsBody):
    return await conversation_service.delete_many(
        auth=request.state.auth,
        body=body,
    )
