import asyncio
import gc
import time

from pymongo.errors import PyMongoError

from src.models.ingestion import ExtractMetadataInput
from src.models.task import Task, TaskError, TaskStatus, TaskType
from src.repositories.context import ContextRepository
from src.repositories.document import DocumentRepository
from src.repositories.project import ProjectRepository
from src.repositories.task import TaskRepository
from src.services.ingestion import IngestionService
from src.util.logger import Logger
from src.util.mongo import client

task_repository = TaskRepository()
document_repository = DocumentRepository()
context_repository = ContextRepository()
project_repository = ProjectRepository()
ingestion_service = IngestionService()


async def index_pending_contexts():
    logger = Logger("IndexPendingContextsTask")

    try:
        tasks = await task_repository.get_priority_tasks(
            status=TaskStatus.PENDING,
            type=TaskType.CONTEXT,
        )

        if not tasks:
            return

        logger.info(f"Starting to index {len(tasks)} contexts")

        await asyncio.gather(*[index_context(task) for task in tasks])
    except Exception as e:
        logger.error("Error indexing pending contexts", e)

    return


async def index_context(task: Task) -> None:
    logger = Logger("IndexContextTask")
    start_time = time.time()

    try:
        async with await client.start_session() as session:
            async with session.start_transaction():
                try:
                    await task_repository.update_one(
                        task_id=task.id,
                        session=session,
                        status=TaskStatus.IN_PROGRESS,
                    )
                    await session.commit_transaction()
                except Exception:
                    await session.abort_transaction()
                    return

            async with session.start_transaction():
                try:
                    await task_repository.update_one(
                        task_id=task.id,
                        session=session,
                        status=TaskStatus.PROCESSING,
                    )
                    await session.commit_transaction()
                except Exception:
                    await session.abort_transaction()
                    return

        context = await context_repository.find_one(context_id=task.context_id)
        if not context:
            error_message = f"Context {task.context_id} not found"
            logger.error(message=error_message, log=True)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.CONTEXT_NOT_FOUND,
                error_message=error_message,
                duration=time.time() - start_time,
            )
            return

        async def get_document():
            if context.document_id:
                return await document_repository.find_one(
                    document_id=context.document_id
                )
            return None

        (document, project) = await asyncio.gather(
            get_document(),
            project_repository.find_one(project_id=context.project_id),
        )

        if not project:
            error_message = f"Project {context.project_id} not found"
            logger.error(error_message)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.PROJECT_NOT_FOUND,
                error_message=error_message,
                duration=time.time() - start_time,
            )
            return

        parse_time = time.time()
        parsed_contexts = None
        try:
            logger.info(f"Parsing context {context.id}")
            parsed_contexts = await ingestion_service.parse(
                content=context.text.encode("utf-8"),
                file_name=f"context_{context.id}.txt",
            )
        except Exception as e:
            error_message = f"Error parsing context {context.id}"
            logger.error(error_message, e)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.CANNOT_PARSE,
                error_message=f"{error_message} - {str(e)}",
                duration=time.time() - start_time,
            )
            return

        if not parsed_contexts:
            error_message = f"Failed to parse context {context.id}"
            logger.error(error_message)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.NO_PARSED_CONTENT,
                error_message=error_message,
                duration=time.time() - start_time,
            )
            return

        logger.info(
            f"Parsed context {context.id} in {time.time() - parse_time} seconds"
        )

        index_time = time.time()
        result = None
        try:
            logger.info(f"Indexing context {context.id}")
            result = await ingestion_service.ingest(
                parsed_documents=parsed_contexts,
                input=ExtractMetadataInput(
                    organization_id=project.organization_id,
                    project_id=context.project_id,
                    user_id=context.user_id,
                    context_id=context.id,
                    conversation_id=context.conversation_id,
                    external_id=context.external_id,
                    document_id=document.id if document else None,
                    metadata=document.metadata if document else None,
                    file_name=document.name if document else None,
                    file_slug=document.slug if document else None,
                ),
            )
        except Exception as e:
            error_message = f"Error indexing context {context.id}"
            logger.error(error_message, e)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.CANNOT_INDEX,
                error_message=f"{error_message} - {str(e)}",
                duration=time.time() - start_time,
            )
            return

        del parsed_contexts
        gc.collect()

        if not result:
            error_message = f"Failed to index context {context.id}"
            logger.error(error_message)
            await task_repository.update_one(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=TaskError.NO_EMBEDDINGS,
                error_message=error_message,
                duration=time.time() - start_time,
            )
            return

        logger.info(
            f"Indexed context {context.id} in {time.time() - index_time} seconds"
        )

        del result
        gc.collect()

        duration = time.time() - start_time
        logger.info(f"Context {context.id} ready in {duration} seconds total")
        await task_repository.update_one(
            task_id=task.id,
            status=TaskStatus.DONE,
            duration=duration,
            delete_error=True,
        )
    except PyMongoError:
        return
    except Exception as e:
        duration = time.time() - start_time
        logger.error("Error indexing context", e)
        await task_repository.update_one(
            task_id=task.id,
            status=TaskStatus.FAILED,
            error=TaskError.ERROR,
            error_message=str(e),
            duration=duration,
        )
