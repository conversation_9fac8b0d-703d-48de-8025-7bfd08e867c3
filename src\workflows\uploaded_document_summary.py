import io
import os
import smtplib
from datetime import datetime, timedelta
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from llama_index.core.workflow import (
    Context,
    Event,
    StartEvent,
    StopEvent,
    Workflow,
    step,
)
from pytz import utc
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.lib.units import mm
from reportlab.platypus import Paragraph, SimpleDocTemplate, Spacer, Table, TableStyle

from src.models.document import DocumentType
from src.models.rag_query import RAGSearchKeywords
from src.models.shared import ConfigModel
from src.repositories.document import DocumentRepository
from src.services.email import EmailService
from src.util.logger import Logger


class UploadedDocumentSummaryInput(ConfigModel):
    project_id: str
    document_types: list[DocumentType]
    days_back: int  # 1 to 31
    user_email: str
    name: str


class FindDocumentsEvent(Event):
    input: UploadedDocumentSummaryInput


class ProcessDocumentsEvent(Event):
    input: UploadedDocumentSummaryInput


class GeneratePdfEvent(Event):
    input: UploadedDocumentSummaryInput


class SendEmailEvent(Event):
    input: UploadedDocumentSummaryInput
    pdf_data: bytes
    document_count: int


class UploadedDocumentSummaryOutput(ConfigModel):
    pdf_data: bytes
    document_count: int
    email_sent: bool = False


EMAIL_BODY = """
Document Summary Report

This report contains a summary of {document_count} documents:
- Agent name: {name}
- Time period: Last {days_back} days
- Generated on: {utc_timestamp} UTC
"""


class UploadedDocumentSummaryWorkflow(Workflow):
    def __init__(self):
        super().__init__(timeout=180)
        self.logger = Logger("UploadedDocumentSummaryWorkflow")
        self.document_repository = DocumentRepository()
        self.email_service = EmailService()

    @step
    async def start(
        self, ctx: Context, ev: StartEvent
    ) -> FindDocumentsEvent | StopEvent:
        if ev.input.days_back < 1 or ev.input.days_back > 31:
            self.logger.error(
                f"Invalid days_back value: {ev.input.days_back}. Must be between 1 and 31."
            )
            return StopEvent(
                result=UploadedDocumentSummaryOutput(
                    pdf_data=b"",
                    document_count=0,
                )
            )

        if not ev.input.document_types:
            self.logger.error("Document types list cannot be empty.")
            return StopEvent(
                result=UploadedDocumentSummaryOutput(
                    pdf_data=b"",
                    document_count=0,
                )
            )

        return FindDocumentsEvent(input=ev.input)

    @step
    async def find_documents(
        self, ctx: Context, ev: FindDocumentsEvent
    ) -> ProcessDocumentsEvent | StopEvent:
        start_date = datetime.now() - timedelta(days=ev.input.days_back)
        documents = await self.document_repository.find_many(
            project_id=ev.input.project_id,
            start_date=start_date,
            keywords=RAGSearchKeywords(
                document_types=ev.input.document_types,
            ),
        )

        if not documents:
            return SendEmailEvent(
                input=ev.input,
                pdf_data=b"",
                document_count=0,
            )

        await ctx.set("documents", documents)
        return ProcessDocumentsEvent(input=ev.input)

    @step
    async def process_documents(
        self, ctx: Context, ev: ProcessDocumentsEvent
    ) -> GeneratePdfEvent:
        documents = await ctx.get("documents")
        documents_summary = []
        for document in documents:
            title = None
            description = None
            companies = None
            for node in document.nodes:
                node_title = node.metadata.get("title")
                if node_title:
                    title = node_title
                node_description = node.metadata.get("description")
                if node_description:
                    description = node_description
                node_companies = node.metadata.get("companies_involved")
                if node_companies:
                    companies = node_companies
                if title and description and companies:
                    break
            keywords = document.keywords
            documents_summary.append(
                {
                    "name": document.name,
                    "types": keywords.document_types if keywords else [],
                    "created_at": document.created_at,
                    "title": title,
                    "description": description,
                    "companies": companies,
                }
            )
        await ctx.set("documents_summary", documents_summary)
        return GeneratePdfEvent(input=ev.input)

    @step
    async def generate_pdf(
        self, ctx: Context, ev: GeneratePdfEvent
    ) -> SendEmailEvent | StopEvent:
        documents_summary = await ctx.get("documents_summary")
        pdf_data = self._generate_pdf_summary(documents_summary, ev.input)
        document_count = len(documents_summary)
        return SendEmailEvent(
            input=ev.input,
            pdf_data=pdf_data,
            document_count=document_count,
        )

    @step
    async def send_email(self, ctx: Context, ev: SendEmailEvent) -> StopEvent:
        subject = f"Document Summary Report - {ev.input.name} - last {ev.input.days_back} days"

        body = EMAIL_BODY.format(
            document_count=ev.document_count,
            name=ev.input.name,
            days_back=ev.input.days_back,
            utc_timestamp=datetime.now(utc).strftime("%Y-%m-%d %H:%M:%S"),
        )

        date_str = datetime.now().strftime("%Y%m%d")
        filename = f"document_summary_{date_str}.pdf"

        email_sent = await self.email_service.send_email(
            recipient_email=ev.input.user_email,
            subject=subject,
            body=body,
            attachment_data=ev.pdf_data,
            attachment_name=filename,
        )

        if email_sent:
            self.logger.info(f"Email sent to {ev.input.user_email} with subject {subject}")
        else:
            self.logger.error(f"Failed to send email to {ev.input.user_email} with subject {subject}")

        return StopEvent(
            result=UploadedDocumentSummaryOutput(
                pdf_data=ev.pdf_data,
                document_count=ev.document_count,
                email_sent=email_sent,
            )
        )

    def _generate_pdf_summary(
        self, documents_summary: list[dict], input_data: UploadedDocumentSummaryInput
    ) -> bytes:
        """Generate a PDF with a table summarizing the documents.

        Args:
            documents_summary: List of document summary dictionaries
            input_data: The original input request data

        Returns:
            The PDF file contents as bytes
        """
        buffer = io.BytesIO()

        # Use A4 landscape for better table fit
        page_width, page_height = landscape(A4)

        # Create the PDF document with A4 size
        doc = SimpleDocTemplate(
            buffer,
            pagesize=landscape(A4),
            rightMargin=15 * mm,
            leftMargin=15 * mm,
            topMargin=20 * mm,  # Increase top margin for better spacing
            bottomMargin=20 * mm,  # Increase bottom margin
        )

        # Define custom colors for a more professional look
        primary_color = colors.HexColor("#2C3E50")  # Dark blue-gray
        accent_color = colors.HexColor("#3498DB")  # Bright blue
        header_bg_color = colors.HexColor("#ECF0F1")  # Light gray with blue tint
        alt_row_color = colors.HexColor("#F9F9F9")  # Very light gray

        # Get styles and create custom styles
        styles = getSampleStyleSheet()

        # Create custom title style
        title_style = ParagraphStyle(
            name="CustomTitle",
            parent=styles["Heading1"],
            fontSize=18,
            leading=22,
            textColor=primary_color,
            spaceAfter=5 * mm,
            alignment=1,  # Center alignment
        )

        # Create custom subtitle style
        subtitle_style = ParagraphStyle(
            name="CustomSubtitle",
            parent=styles["Heading2"],
            fontSize=12,
            leading=14,
            textColor=accent_color,
            spaceAfter=2 * mm,
            alignment=1,  # Center alignment
        )

        # Create timestamp style
        timestamp_style = ParagraphStyle(
            name="TimestampStyle",
            parent=styles["Normal"],
            fontSize=9,
            textColor=colors.gray,
            alignment=1,  # Center alignment
            spaceAfter=10 * mm,
        )

        # Create a custom style for table cells to handle wrapping
        cell_style = ParagraphStyle(
            name="CellStyle",
            parent=styles["Normal"],
            fontSize=9,
            leading=11,
            wordWrap="CJK",
        )

        # Create header cell style
        header_style = ParagraphStyle(
            name="HeaderStyle",
            parent=styles["Normal"],
            fontSize=10,
            leading=12,
            fontName="Helvetica-Bold",
            textColor=primary_color,
            alignment=1,  # Center alignment
        )

        # Adjust column widths to fit content better
        available_width = page_width - (30 * mm)  # Total width minus margins
        col_widths = [
            available_width * 0.15,  # Document Name (15%)
            available_width * 0.12,  # Document Type (12%)
            available_width * 0.12,  # Created At (12%)
            available_width * 0.18,  # Title (18%)
            available_width * 0.30,  # Description (30%)
            available_width * 0.13,  # Companies Involved (13%)
        ]

        # Create styled header cells with sentence case for the first word only
        header_cells = [
            Paragraph("Document name", header_style),
            Paragraph("Document type", header_style),
            Paragraph("Created at", header_style),
            Paragraph("Title", header_style),
            Paragraph("Description", header_style),
            Paragraph("Companies involved", header_style),
        ]

        # Prepare data for the table
        table_data = [header_cells]

        # Add document data rows
        for doc_summary in documents_summary:
            # Format document types as comma-separated string
            doc_types = (
                ", ".join(doc_type.value for doc_type in doc_summary["types"])
                if doc_summary["types"]
                else ""
            )

            # Format created_at date
            created_at = (
                doc_summary["created_at"].strftime("%Y-%m-%d %H:%M")
                if doc_summary["created_at"]
                else ""
            )

            # Format companies list if it exists - properly handle list of strings
            companies = ""
            if doc_summary["companies"]:
                # Ensure companies is treated as a list
                companies_list = doc_summary["companies"]
                if isinstance(companies_list, list):
                    companies = ", ".join(companies_list)
                else:
                    # If for some reason it's not a list, convert to string
                    companies = str(companies_list)

            # Format and truncate description if too long
            description = doc_summary["description"] or ""
            if len(description) > 150:
                description = description[:147] + "..."

            # Format and truncate title if too long
            title = doc_summary["title"] or ""
            if len(title) > 60:
                title = title[:57] + "..."

            # Wrap cell contents in Paragraph objects for proper word wrapping
            name_cell = Paragraph(doc_summary["name"], cell_style)
            doc_types_cell = Paragraph(doc_types, cell_style)
            created_at_cell = Paragraph(created_at, cell_style)
            title_cell = Paragraph(title, cell_style)
            description_cell = Paragraph(description, cell_style)
            companies_cell = Paragraph(companies, cell_style)

            # Add row to table data
            table_data.append(
                [
                    name_cell,
                    doc_types_cell,
                    created_at_cell,
                    title_cell,
                    description_cell,
                    companies_cell,
                ]
            )

        # Create the table with defined column widths
        table = Table(table_data, repeatRows=1, colWidths=col_widths)

        # Style the table
        table_style = TableStyle(
            [
                # Overall table styling
                ("BACKGROUND", (0, 0), (-1, 0), header_bg_color),
                ("TEXTCOLOR", (0, 0), (-1, 0), primary_color),
                ("ALIGN", (0, 0), (-1, 0), "CENTER"),
                ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                ("FONTSIZE", (0, 0), (-1, 0), 10),
                # Body styling
                ("FONTNAME", (0, 1), (-1, -1), "Helvetica"),
                ("FONTSIZE", (0, 1), (-1, -1), 9),
                ("ALIGN", (0, 1), (-1, -1), "LEFT"),
                ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
                # Borders - more subtle borders for a cleaner look
                ("GRID", (0, 0), (-1, -1), 0.3, colors.lightgrey),
                ("LINEBELOW", (0, 0), (-1, 0), 1, primary_color),  # Colored header line
                # Alternating row colors
                ("ROWBACKGROUNDS", (0, 1), (-1, -1), [colors.white, alt_row_color]),
                # Word wrapping
                ("LEFTPADDING", (0, 0), (-1, -1), 4),
                ("RIGHTPADDING", (0, 0), (-1, -1), 4),
                ("TOPPADDING", (0, 0), (-1, -1), 4),
                ("BOTTOMPADDING", (0, 0), (-1, -1), 4),
            ]
        )

        # Apply the style to the table
        table.setStyle(table_style)

        # Create content elements for the document
        elements = []

        # Add title
        title_text = "Document Summary Report"
        elements.append(Paragraph(title_text, title_style))

        # Add subtitle with filter information - omit project ID
        subtitle_text = f"Agent name: {input_data.name} | Time period: Last {input_data.days_back} days"
        elements.append(Paragraph(subtitle_text, subtitle_style))

        # Add generation timestamp
        timestamp = f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        elements.append(Paragraph(timestamp, timestamp_style))

        # Add the table to the elements with proper spacing
        elements.append(table)

        # Add some space at the bottom
        elements.append(Spacer(1, 10 * mm))

        # Build the PDF document
        doc.build(elements)

        # Get the PDF content
        pdf_data = buffer.getvalue()
        buffer.close()

        return pdf_data
