from llama_index.embeddings.gemini import GeminiEmbedding
from llama_index.embeddings.openai import OpenAIEmbedding, OpenAIEmbeddingModelType
from llama_index.llms.anthropic import Anthropic
from llama_index.llms.gemini import Gemini
from llama_index.llms.groq import <PERSON><PERSON>q
from llama_index.llms.openai import OpenAI
from src.models.settings import LLMModel
# from src.rag.grok_llm import Grok

# Default LLMs
OPEN_AI_LLM = OpenAI(model=LLMModel.GPT_4_1_MINI, temperature=0.4, max_retries=10)
GEMINI_LLM = Gemini(model=LLMModel.GEMINI_2_0_FLASH, temperature=1.0, max_tokens=8192)
GROQ_LLM = Groq(model=LLMModel.DEEPSEEK_R1_DISTILL_LLAMA_70B)
ANTHROPIC_LLM = Anthropic(model=LLMModel.CLAUDE_3_7_SONNET)
# GROK_LLM = Grok(model=LLMModel.GROK_3_MINI, temperature=0.7, max_tokens=8192)
DEFAULT_LLM = OPEN_AI_LLM

# Default Embeddings
OPEN_AI_EMBED_MODEL = OpenAIEmbedding(
    model=OpenAIEmbeddingModelType.TEXT_EMBED_3_LARGE,
    max_retries=10,
)
DEFAULT_EMBED_MODEL = OPEN_AI_EMBED_MODEL

# Default Chunking
CHUNK_SIZE = 512
CHUNK_OVERLAP = 128
