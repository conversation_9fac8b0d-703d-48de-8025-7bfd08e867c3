import asyncio
from datetime import datetime

from bson import ObjectId
from llama_index.core.schema import TextNode
from pymongo import DESCENDING

from src.models.document import (
    ContentType,
    Document,
    DocumentKeywords,
    DocumentUsageResponse,
    InsertDocumentInput,
    UpdateDocumentInput,
)
from src.models.rag_query import RAGSearchKeywords
from src.models.shared import Pagination, PaginationResponse, PaginationResult
from src.util.date import mongo_date
from src.util.mongo import db


class DocumentRepository:
    def __init__(self):
        self.collection = db.get_collection("documents")

    def _to_model(self, document_dict: dict) -> Document:
        conversation_id = document_dict.get("conversation_id")
        if conversation_id:
            conversation_id = str(conversation_id)
        user_id = document_dict.get("user_id")
        if user_id:
            user_id = str(user_id)
        return Document(
            id=str(document_dict.get("_id")),
            project_id=str(document_dict.get("project_id")),
            name=document_dict.get("name"),
            slug=document_dict.get("slug"),
            path=document_dict.get("path"),
            url=document_dict.get("url"),
            content_type=document_dict.get("content_type"),
            size=document_dict.get("size"),
            source_url=document_dict.get("source_url"),
            conversation_id=conversation_id,
            user_id=user_id,
            external_id=document_dict.get("external_id"),
            keywords=DocumentKeywords.from_dict(document_dict.get("keywords")),
            nodes=[
                TextNode().from_dict(node) for node in document_dict.get("nodes") or []
            ],
            created_at=document_dict.get("created_at"),
        )

    async def find_one(
        self,
        document_id: str | None = None,
        project_id: str | None = None,
        conversation_id: str | None = None,
        user_id: str | None = None,
        external_id: str | None = None,
        slug: str | None = None,
    ) -> Document | None:
        filter = {}
        if document_id:
            filter["_id"] = ObjectId(document_id)
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if conversation_id:
            filter["conversation_id"] = ObjectId(conversation_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        if external_id:
            filter["external_id"] = external_id
        if slug:
            filter["slug"] = slug
        document_dict = await self.collection.find_one(filter=filter)
        return self._to_model(document_dict) if document_dict else None

    async def find_many(
        self,
        document_ids: list[str] = [],
        external_ids: list[str] = [],
        project_id: str | None = None,
        conversation_id: str | None = None,
        user_id: str | None = None,
        keywords: RAGSearchKeywords | None = None,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        limit: int = 1000,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[Document]:
        filter = {}
        if document_ids:
            filter["_id"] = {
                "$in": [ObjectId(document_id) for document_id in document_ids]
            }
        if external_ids:
            filter["external_id"] = {"$in": external_ids}
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if conversation_id:
            filter["conversation_id"] = ObjectId(conversation_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        if keywords:
            if keywords.date_range:
                date_filter = {}
                if keywords.date_range.start_date:
                    date_filter["$gte"] = keywords.date_range.start_date
                if keywords.date_range.end_date:
                    date_filter["$lte"] = keywords.date_range.end_date
                filter["keywords.document_date"] = date_filter
            or_conditions = []
            if keywords.document_types:
                for document_type in keywords.document_types:
                    or_conditions.append(
                        {"keywords.document_types": {"$in": [document_type.value]}}
                    )
            if keywords.document_categories:
                for document_category in keywords.document_categories:
                    or_conditions.append(
                        {
                            "keywords.document_categories": {
                                "$in": [document_category.value]
                            }
                        }
                    )
            if keywords.entity_ids:
                for entity_id in keywords.entity_ids:
                    or_conditions.append({"keywords.entity_ids": {"$in": [entity_id]}})
            if or_conditions:
                filter["$or"] = or_conditions

        date_filter = {}
        if start_date:
            date_filter["$gte"] = mongo_date(start_date)
        if end_date:
            date_filter["$lte"] = mongo_date(end_date)
        if date_filter:
            filter["created_at"] = date_filter

        document_dicts = await self.collection.find(
            filter=filter, sort=sort, limit=limit
        ).to_list()

        return [self._to_model(document_dict) for document_dict in document_dicts]

    async def paginate(
        self,
        project_id: str | None = None,
        document_ids: list[str] = [],
        external_ids: list[str] = [],
        conversation_id: str | None = None,
        user_id: str | None = None,
        size: float | None = None,  # in megabytes
        content_type: ContentType | None = None,
        name: str | None = None,
        start_date: str | None = None,
        end_date: str | None = None,
        page_size: int = 10,
        page_number: int = 1,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> PaginationResponse[Document]:
        date_filter = {}
        if start_date:
            date_filter["$gte"] = mongo_date(start_date)
        if end_date:
            date_filter["$lte"] = mongo_date(end_date)

        filter = {}
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if document_ids:
            filter["_id"] = {
                "$in": [ObjectId(document_id) for document_id in document_ids]
            }
        if external_ids:
            filter["external_id"] = {"$in": external_ids}
        if name:
            filter["name"] = {"$regex": name, "$options": "i"}
        if conversation_id:
            filter["conversation_id"] = ObjectId(conversation_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        if date_filter:
            filter["created_at"] = date_filter
        if size:
            filter["size"] = {"$gte": size * 1024 * 1024}
        if content_type:
            filter["content_type"] = content_type.value

        pagination = Pagination(page_size=page_size, page_number=page_number)

        (total, document_dicts) = await asyncio.gather(
            self.collection.count_documents(filter=filter),
            self.collection.find(
                filter=filter,
                limit=pagination.limit(),
                skip=pagination.skip(),
                sort=sort,
            ).to_list(),
        )

        return PaginationResponse(
            data=[self._to_model(document_dict) for document_dict in document_dicts],
            pagination=PaginationResult(
                total=total,
                page_size=page_size,
                page_number=page_number,
            ),
        )

    async def usage(
        self, project_id: str, user_id: str | None = None
    ) -> DocumentUsageResponse:
        aggregate_id = project_id
        match_filter = {
            "project_id": ObjectId(project_id),
        }
        if user_id:
            aggregate_id = f"{project_id}-{user_id}"
            match_filter["user_id"] = ObjectId(user_id)
        results = await self.collection.aggregate(
            [
                {
                    "$match": match_filter,
                },
                {
                    "$group": {
                        "_id": aggregate_id,
                        "total_count": {"$sum": 1},
                        "total_size": {"$sum": "$size"},
                    }
                },
            ]
        ).to_list(1)
        docs = [DocumentUsageResponse(**result) for result in results]
        return docs[0] if docs else DocumentUsageResponse(total_count=0, total_size=0)

    async def insert_one(self, input: InsertDocumentInput) -> Document:
        result = await self.collection.insert_one(input.to_dict())
        return await self.find_one(document_id=result.inserted_id)

    async def insert_many(
        self,
        inputs: list[InsertDocumentInput],
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[Document]:
        document_dicts = [input.to_dict() for input in inputs]
        result = await self.collection.insert_many(document_dicts)
        return await self.find_many(document_ids=result.inserted_ids, sort=sort)

    async def update_one(
        self,
        document_id: str,
        input: UpdateDocumentInput,
        project_id: str | None = None,
    ) -> Document | None:
        filter = {"_id": ObjectId(document_id)}
        if project_id:
            filter["project_id"] = ObjectId(project_id)

        update = {}
        if input.name:
            update["name"] = input.name
        if input.source_url:
            update["source_url"] = input.source_url
        if input.conversation_id:
            update["conversation_id"] = input.conversation_id
        if input.user_id:
            update["user_id"] = input.user_id
        if input.external_id:
            update["external_id"] = input.external_id
        if input.metadata:
            update["metadata"] = input.metadata
        if input.keywords:
            update["keywords"] = input.keywords.to_dict()
        if input.nodes:
            update["nodes"] = [node.to_dict() for node in input.nodes]

        await self.collection.update_one(filter=filter, update={"$set": update})
        return await self.find_one(document_id=document_id, project_id=project_id)

    async def delete_one(
        self,
        document_id: str | None = None,
        project_id: str | None = None,
        conversation_id: str | None = None,
        user_id: str | None = None,
        external_id: str | None = None,
        slug: str | None = None,
    ) -> Document | None:
        document = await self.find_one(
            document_id=document_id,
            project_id=project_id,
            conversation_id=conversation_id,
            user_id=user_id,
            external_id=external_id,
            slug=slug,
        )
        if not document:
            return None
        await self.collection.delete_one(filter={"_id": ObjectId(document.id)})
        return document

    async def delete_many(
        self,
        document_ids: list[str] = [],
        external_ids: list[str] = [],
        project_id: str | None = None,
        conversation_id: str | None = None,
        user_id: str | None = None,
        sort: list[tuple[str, int]] = [("created_at", DESCENDING)],
    ) -> list[Document]:
        documents = await self.find_many(
            document_ids=document_ids,
            external_ids=external_ids,
            project_id=project_id,
            conversation_id=conversation_id,
            user_id=user_id,
            sort=sort,
        )
        if not documents:
            return []
        await self.collection.delete_many(
            filter={"_id": {"$in": [ObjectId(document.id) for document in documents]}}
        )
        return documents
