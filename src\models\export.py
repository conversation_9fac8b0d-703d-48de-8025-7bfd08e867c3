from datetime import datetime

from bson import ObjectId
from pytz import utc

from src.models.shared import ConfigModel


class Export(ConfigModel):
    id: str
    user_id: str
    url: str
    created_at: datetime = datetime.now(utc)


class ExportResponse(ConfigModel):
    id: str
    user_id: str
    url: str
    created_at: datetime


class InsertExportInput(ConfigModel):
    user_id: str
    url: str

    def to_dict(self) -> dict:
        return {
            "user_id": ObjectId(self.user_id),
            "url": self.url,
            "created_at": datetime.now(utc),
        }
