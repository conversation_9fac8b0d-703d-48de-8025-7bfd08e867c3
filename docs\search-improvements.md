# Query Expansion

Query expansion involves refining a user's query by adding related terms or phrases to improve search results. This process enhances search performance by incorporating additional context and relevant keywords, making it easier to match queries with knowledge base content.

## 1 - Query Decomposition

Breaking down a complex question into smaller, simpler sub-questions improves semantic search accuracy.

**Example:** For the question, _"What is the capital of the country where the inventor of the telephone was born?"_

1. Who invented the telephone?
2. Where was the inventor of the telephone born?
3. What is the capital of that country?

**Use Case:** This method ensures each part of a complex query is addressed separately, leading to more accurate and comprehensive answers.

## 2 - HyDE (Hypothetical Document Embedding)

HyDE uses a Large Language Model (LLM) to generate a hypothetical passage that captures the query's context and intent, improving semantic search.

**Example:**

- **Question:** What is Ars-HDGunn structure?
- **Generated Passage:** The Ars-HDGunn structure is a specialized architectural design that integrates advanced materials and innovative engineering techniques to create an efficient and sustainable building. It features a blend of aesthetic appeal and functional performance, often incorporating elements like green roofs, solar panels, and energy-efficient systems.

**Use Case:** This approach helps find better semantic matches by providing richer context, especially in cases where the model has not been fine-tuned on specific data.

## 3 - Synonym Expansion

Adding synonyms of key query terms helps capture different ways users might express the same concept.

**Use Case:** Expands search coverage by including alternate wordings, ensuring relevant documents using different terms are retrieved.

## 4 - Stemming and Lemmatization

Considering different word forms ensures variations in grammar and tense do not impact search results.

**Use Case:** Improves retrieval by accounting for words with different inflections, such as "run," "running," and "ran."

## 5 - Thesauri and Ontologies

Using structured vocabularies to include related terms and concepts improves the breadth of search results.

**Use Case:** Enhances search accuracy by incorporating hierarchical relationships, such as broader and narrower concepts.

# Search Strategies

Different search strategies can be used depending on the query type and the user's context. If a user has already asked questions or selected documents in the UI, the search strategy can be adjusted accordingly.

If there is no prior context, prioritizing knowledge base searches is recommended. If context is available, knowledge base aggregation can provide a more relevant response.

## 1 - General Questions

Queries not related to the knowledge base.

**Example:** Who discovered America?

**Strategy:** Retrieve the answer directly from the LLM.

**Enhancement:** If the LLM’s confidence is low, perform a secondary search in the knowledge base to verify the answer or provide additional context. A web search can be used to find the answer otherwise.

## 2 - Knowledge Base Questions

Queries related to stored knowledge.

**Example:** Who owns the construction company in the document "Project Wember"?

**Strategy:** Use semantic search with Pinecone, then rerank the results with Cohere Rerank to find the best contextual answer.

**Enhancements:**

- Apply query decomposition to better handle complex queries.
- Use relevance feedback mechanisms to refine search results based on user interactions.

## 3 - Knowledge Base Aggregation

Queries that require analyzing an entire document.

**Examples:**

- Summarize the document "New office construction invoice."
- How much has been spent on Project Wember?

**Strategy:** First, filter results using keywords, then perform a hybrid search with Pinecone to find the best matches. Finally, load the full document into context to generate a response.

**Enhancements:**

- Utilize HyDE to create hypothetical documents that provide better semantic matches.
- Implement document clustering techniques to group related documents, improving summarization and comprehensive answers.

## 4 - Fallback

If a query lacks sufficient context, provide users with examples of how to structure their search.

**Enhancements:**

- Implement an interactive query refinement feature that guides users to improve their searches iteratively.
- Conduct a basic semantic search and present the top results, allowing users to refine their query by selecting relevant documents.
