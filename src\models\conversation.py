from datetime import datetime

from bson import ObjectId
from pytz import utc

from src.models.document import DocumentResponse
from src.models.message import InsertMessageInput, Message, MessageResponse
from src.models.shared import ConfigModel, DateCount

DEFAULT_CONVERSATION_TITLE = "New chat"


class DocumentScore(ConfigModel):
    document_id: str
    score: float
    name: str
    slug: str
    size: float
    content_type: str
    url: str
    text: str
    source_url: str | None = None

    def to_dict(self) -> dict:
        return {
            "document_id": ObjectId(self.document_id),
            "score": self.score,
            "name": self.name,
            "slug": self.slug,
            "size": self.size,
            "content_type": self.content_type,
            "url": self.url,
            "text": self.text,
            "source_url": self.source_url,
        }


class Conversation(ConfigModel):
    id: str
    project_id: str
    user_id: str
    title: str = DEFAULT_CONVERSATION_TITLE
    messages: list[Message] = []
    scores: list[DocumentScore] = []
    created_at: datetime = datetime.now(utc)

    def to_dict(self) -> dict:
        return {
            "id": ObjectId(self.id),
            "project_id": ObjectId(self.project_id),
            "user_id": ObjectId(self.user_id),
            "title": self.title,
            "messages": [message.to_dict() for message in self.messages],
            "scores": [score.to_dict() for score in self.scores],
            "created_at": self.created_at,
        }

    def get_last_messages(self, count: int) -> list[Message]:
        self.messages.sort(key=lambda x: x.created_at.replace(tzinfo=utc))
        return self.messages[-count:] if len(self.messages) > count else self.messages


class ConversationResponse(ConfigModel):
    id: str
    title: str
    messages: list[MessageResponse] = []
    scores: list[DocumentScore] = []
    documents: list[DocumentResponse] = []
    user_id: str | None = None
    created_at: datetime = datetime.now(utc)


class ConversationUsageResponse(ConfigModel):
    total_count: int
    per_day: list[DateCount] = []


class InsertConversationInput(ConfigModel):
    project_id: str
    user_id: str
    title: str
    messages: list[InsertMessageInput] = []

    def to_dict(self) -> dict:
        return {
            "project_id": ObjectId(self.project_id),
            "user_id": ObjectId(self.user_id),
            "title": self.title,
            "messages": [message.to_dict() for message in self.messages],
            "scores": [],
            "created_at": datetime.now(utc),
        }


class InsertDocumentScoreInput(ConfigModel):
    document_id: str
    score: float
    name: str
    slug: str
    size: float
    content_type: str
    url: str
    text: str
    source_url: str | None = None

    def to_dict(self) -> dict:
        return {
            "document_id": ObjectId(self.document_id),
            "score": self.score,
            "name": self.name,
            "slug": self.slug,
            "size": self.size,
            "content_type": self.content_type,
            "url": self.url,
            "text": self.text,
            "source_url": self.source_url,
        }


class UpdateConversationInput(ConfigModel):
    title: str | None = None
    add_scores: list[InsertDocumentScoreInput] = []
    add_messages: list[InsertMessageInput] = []


class CreateConversationBody(ConfigModel):
    title: str | None = None


class UpdateConversationBody(ConfigModel):
    title: str | None = None


class DeleteConversationsBody(ConfigModel):
    conversation_ids: list[str] = []
