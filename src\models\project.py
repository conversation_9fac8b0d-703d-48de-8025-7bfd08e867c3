from datetime import datetime

from bson import ObjectId
from pytz import utc

from src.models.shared import ConfigModel


class Project(ConfigModel):
    id: str
    organization_id: str
    name: str
    slug: str
    created_at: datetime = datetime.now(utc)


class InsertProjectInput(ConfigModel):
    organization_id: str
    name: str
    slug: str

    def to_dict(self) -> dict:
        return {
            "organization_id": ObjectId(self.organization_id),
            "name": self.name,
            "slug": self.slug,
            "created_at": datetime.now(utc),
        }
