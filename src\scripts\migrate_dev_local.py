import asyncio
import os
import secrets
import uuid
from datetime import datetime

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient
from pytz import utc

from src.models.task import TaskAction, TaskPriority, TaskStatus
from src.services.pinecone import PineconeService
from src.util.text import slugify

client = AsyncIOMotorClient(os.environ.get("MONGO_URI"))
local_db = client["document_db"]
backup_db = client["document_db_backup"]

env = "development"
old_org_to_new_org_map = {}
new_org_to_old_org_map = {}
old_org_to_project_map = {}
project_to_old_org_map = {}
old_user_to_new_user_map = {}


def new_date():
    return datetime.now(utc)


def generate_api_key() -> str:
    key = str(uuid.uuid4()) + "-" + secrets.token_urlsafe(32)
    if env == "development":
        return "snapz-dev-" + key
    if env == "staging":
        return "snapz-stg-" + key
    if env == "production":
        return "snapz-prod-" + key
    return "snapz-local-" + key


async def reset():
    await asyncio.gather(
        local_db.drop_collection("organizations"),
        local_db.drop_collection("projects"),
        local_db.drop_collection("users"),
        local_db.drop_collection("documents"),
        local_db.drop_collection("contexts"),
        local_db.drop_collection("tasks"),
        local_db.drop_collection("conversations"),
        local_db.drop_collection("exports"),
    )


async def migrate_dev_local():
    print("Resetting collections")
    await reset()

    print("Migrating organizations")
    await migrate_organizations()

    print("Migrating users")
    await migrate_users()

    # print("Migrating documents")
    # await migrate_documents()

    # print("Migrating contexts")
    # await migrate_contexts()

    # print("Migrating tasks")
    # await migrate_tasks()

    # print("Migrating pinecone")
    # await migrate_pinecone()

    print("Finished migration")


async def migrate_organizations():
    organization_collection = backup_db.get_collection("organizations")
    new_organization_collection = local_db.get_collection("organizations")
    new_project_collection = local_db.get_collection("projects")

    old_organizations = await organization_collection.find().to_list()

    for old_organization in old_organizations:
        new_organization_result = await new_organization_collection.insert_one(
            {
                "clerk_organization_id": old_organization.get("_id"),
                "api_keys": [
                    {
                        "api_key": old_organization.get("api_key"),
                        "created_at": new_date(),
                    },
                    {
                        "api_key": generate_api_key(),
                        "created_at": new_date(),
                    },
                ],
                "created_at": new_date(),
            }
        )

        new_organization_id = new_organization_result.inserted_id
        old_organization_id = old_organization.get("_id")
        old_org_to_new_org_map[old_organization_id] = new_organization_id
        new_org_to_old_org_map[new_organization_id] = old_organization_id

        default_project_name = "Default project"
        new_project_result = await new_project_collection.insert_one(
            {
                "organization_id": ObjectId(new_organization_id),
                "name": default_project_name,
                "slug": slugify(default_project_name),
                "created_at": new_date(),
            }
        )
        default_project_id = new_project_result.inserted_id
        old_org_to_project_map[old_organization_id] = default_project_id
        project_to_old_org_map[default_project_id] = old_organization_id


async def migrate_users():
    user_collection = backup_db.get_collection("users")
    new_user_collection = local_db.get_collection("users")

    old_users = await user_collection.find().to_list()

    for old_user in old_users:
        new_user_result = await new_user_collection.insert_one(
            {
                "clerk_user_id": old_user.get("_id"),
                "accepted_terms": False,
                "created_at": new_date(),
            }
        )
        new_user_id = new_user_result.inserted_id
        old_user_to_new_user_map[old_user.get("_id")] = new_user_id


async def migrate_documents():
    document_collection = backup_db.get_collection("documents")
    new_document_collection = local_db.get_collection("documents")

    old_documents = await document_collection.find().to_list()

    for old_document in old_documents:
        project_id = old_org_to_project_map.get(old_document.get("organization_id"))
        user_id = old_document.get("user_id")
        if user_id:
            user_id = old_user_to_new_user_map.get(user_id)
        await new_document_collection.insert_one(
            {
                "_id": ObjectId(old_document.get("_id")),
                "project_id": ObjectId(project_id),
                "name": old_document.get("original_name"),
                "slug": old_document.get("name"),
                "path": old_document.get("file_path"),
                "url": old_document.get("url"),
                "content_type": old_document.get("content_type"),
                "size": old_document.get("size"),
                "source_url": old_document.get("source_url"),
                "conversation_id": None,
                "user_id": ObjectId(user_id) if user_id else None,
                "external_id": old_document.get("external_id"),
                "metadata": old_document.get("metadata"),
                "created_at": old_document.get("created_at"),
            }
        )


async def migrate_contexts():
    context_collection = backup_db.get_collection("contexts")
    new_context_collection = local_db.get_collection("contexts")

    old_contexts = await context_collection.find().to_list()

    for old_context in old_contexts:
        project_id = old_org_to_project_map.get(old_context.get("organization_id"))
        document_id = old_context.get("document_id")
        user_id = old_context.get("user_id")
        if user_id:
            user_id = old_user_to_new_user_map.get(user_id)
        await new_context_collection.insert_one(
            {
                "project_id": ObjectId(project_id),
                "text": old_context.get("text"),
                "conversation_id": None,
                "document_id": ObjectId(document_id) if document_id else None,
                "user_id": ObjectId(user_id) if user_id else None,
                "external_id": old_context.get("external_id"),
                "created_at": old_context.get("created_at"),
            }
        )


async def migrate_tasks():
    task_collection = backup_db.get_collection("tasks")
    new_task_collection = local_db.get_collection("tasks")
    new_document_collection = local_db.get_collection("documents")
    new_context_collection = local_db.get_collection("contexts")

    old_tasks = await task_collection.find().to_list()

    for old_task in old_tasks:
        document_id = old_task.get("document_id")
        if document_id:
            document = await new_document_collection.find_one(
                {"_id": ObjectId(document_id)}
            )
            if not document:
                continue
        context_id = old_task.get("context_id")
        if context_id:
            context = await new_context_collection.find_one(
                {"_id": ObjectId(context_id)}
            )
            if not context:
                continue
        await new_task_collection.insert_one(
            {
                "_id": ObjectId(old_task.get("_id")),
                "document_id": ObjectId(document_id) if document_id else None,
                "context_id": ObjectId(context_id) if context_id else None,
                "status": old_task.get("status") or TaskStatus.PENDING,
                "priority": old_task.get("priority") or TaskPriority.LOW,
                "action": old_task.get("action") or TaskAction.CREATE,
                "duration": old_task.get("duration") or None,
                "error": old_task.get("error") or None,
                "error_message": old_task.get("error") or None,
                "created_at": old_task.get("created_at") or new_date(),
            }
        )


async def migrate_pinecone():
    pinecone_service = PineconeService()
    documents_collection = backup_db.get_collection("new_documents")
    # contexts_collection = db.get_collection("new_contexts")

    documents = await documents_collection.find().to_list()

    for document in documents:
        document_id = str(document.get("_id"))
        project_id = str(document.get("project_id"))
        old_organization_id = project_to_old_org_map.get(project_id)
        new_organization_id = old_org_to_new_org_map.get(old_organization_id)

        vectors = await pinecone_service.query(
            organization_id=old_organization_id,
            document_ids=[document_id],
        )

        if not vectors:
            continue

        for vector in vectors:
            metadata = vector.get("metadata")
            if not metadata:
                continue
            metadata["app_organization_id"] = new_organization_id
            metadata["app_project_id"] = project_id
            vector["metadata"] = metadata

        print(vectors)
        return

        # await pinecone_service.aupsert(
        #     organization_id=new_organization_id,
        #     vectors=vectors,
        # )


asyncio.run(migrate_dev_local())
