import asyncio
import os

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient

client = AsyncIOMotorClient(os.environ.get("MONGO_URI"))
db = client["document_db"]

conversations_collection = db["conversations"]


async def main():
    conversations = await conversations_collection.find().to_list()
    for conversation in conversations:
        messages = conversation.get("messages", [])
        for message in messages:
            message["_id"] = ObjectId()
            message["context_id"] = None
        await conversations_collection.update_one(
            {"_id": ObjectId(conversation.get("_id"))},
            {"$set": {"messages": messages}},
        )


asyncio.run(main())
