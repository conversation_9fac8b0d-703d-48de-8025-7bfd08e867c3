from fastapi import APIRouter, Form, Query, Request, UploadFile

from src.models.context import ContextResponse, DeleteContextsBody, PostContextsBody
from src.models.document import DeleteDocumentsBody, DocumentResponse, PostDocumentsBody
from src.models.integration import ExternalDeleteResponse, ExternalResponse
from src.models.shared import DeleteResponse, PaginationResponse
from src.services.context import ContextService
from src.services.document import DocumentService
from src.services.integration import IntegrationService

integration_router = APIRouter()
integration_service = IntegrationService()
document_service = DocumentService()
context_service = ContextService()


@integration_router.post("/documents/files", response_model=list[DocumentResponse])
async def post_document_files(
    request: Request,
    files: list[UploadFile],
    source_url: str | None = Form(None),
    conversation_id: str | None = Form(None),
    external_id: str | None = Form(None),
    metadata: str | None = Form(None),
):
    return await document_service.post_files(
        auth=request.state.auth,
        files=files,
        source_url=source_url,
        conversation_id=conversation_id,
        external_id=external_id,
        metadata=metadata,
    )


@integration_router.post("/documents/urls", response_model=list[DocumentResponse])
async def post_document_urls(
    request: Request,
    body: PostDocumentsBody,
):
    return await document_service.post_urls(
        auth=request.state.auth,
        body=body,
    )


@integration_router.get(
    "/documents/paginate", response_model=PaginationResponse[DocumentResponse]
)
async def paginate_documents(
    request: Request,
    name: str | None = None,
    document_ids: list[str] = Query([]),
    external_ids: list[str] = Query([]),
    conversation_id: str | None = None,
    user_id: str | None = None,
    size: float | None = None,
    start_date: str | None = None,
    end_date: str | None = None,
    page_size: int = 10,
    page_number: int = 1,
):
    return await document_service.paginate(
        auth=request.state.auth,
        document_ids=document_ids,
        external_ids=external_ids,
        name=name,
        conversation_id=conversation_id,
        user_id=user_id,
        size=size,
        start_date=start_date,
        end_date=end_date,
        page_size=page_size,
        page_number=page_number,
    )


@integration_router.delete("/documents", response_model=DeleteResponse)
async def delete_many_documents(request: Request, body: DeleteDocumentsBody):
    return await document_service.delete_many(
        auth=request.state.auth,
        body=body,
    )


@integration_router.post("/contexts", response_model=list[ContextResponse])
async def post_many_contexts(request: Request, body: PostContextsBody):
    return await context_service.post_many(
        auth=request.state.auth,
        body=body,
    )


@integration_router.get(
    "/contexts/paginate", response_model=PaginationResponse[ContextResponse]
)
async def paginate_contexts(
    request: Request,
    context_ids: list[str] = Query([]),
    external_ids: list[str] = Query([]),
    user_id: str | None = None,
    conversation_id: str | None = None,
    start_date: str | None = None,
    end_date: str | None = None,
    page_size: int = 10,
    page_number: int = 1,
):
    return await context_service.paginate(
        auth=request.state.auth,
        context_ids=context_ids,
        external_ids=external_ids,
        user_id=user_id,
        conversation_id=conversation_id,
        start_date=start_date,
        end_date=end_date,
        page_size=page_size,
        page_number=page_number,
    )


@integration_router.delete("/contexts", response_model=DeleteResponse)
async def delete_many_contexts(
    request: Request,
    body: DeleteContextsBody,
):
    return await context_service.delete_many(
        auth=request.state.auth,
        body=body,
    )


@integration_router.get("/{external_id}", response_model=ExternalResponse)
async def get_external(request: Request, external_id: str):
    return await integration_service.get_external(
        auth=request.state.auth,
        external_id=external_id,
    )


@integration_router.delete("/{external_id}", response_model=ExternalDeleteResponse)
async def delete_external(request: Request, external_id: str):
    return await integration_service.delete_external(
        auth=request.state.auth,
        external_id=external_id,
    )
