import logging

import sentry_sdk


class Logger:
    def __init__(self, name: str):
        self.name = name
        formatter = logging.Formatter(
            "%(asctime)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        self.logger = logging.getLogger(name=name)
        if not self.logger.hasHandlers():
            self.logger.setLevel(logging.DEBUG)
            handler = logging.StreamHandler()
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

    def _format(self, message: str, request_id: str | None = None) -> str:
        return f"{request_id} - {message}" if request_id else message

    def debug(self, message: str, log: bool = False, request_id: str | None = None):
        formatted_message = self._format(message, request_id)
        self.logger.debug(formatted_message)
        if log:
            sentry_sdk.capture_message(formatted_message, level="debug")

    def info(self, message: str, log: bool = False, request_id: str | None = None):
        formatted_message = self._format(message, request_id)
        self.logger.info(formatted_message)
        if log:
            sentry_sdk.capture_message(formatted_message, level="info")

    def warning(self, message: str, log: bool = False, request_id: str | None = None):
        formatted_message = self._format(message, request_id)
        self.logger.warning(formatted_message)
        if log:
            sentry_sdk.capture_message(formatted_message, level="warning")

    def error(
        self,
        message: str,
        exception: Exception | None = None,
        log: bool = True,
        request_id: str | None = None,
    ):
        if exception:
            message += f": {repr(exception)}"
        formatted_message = self._format(message, request_id)
        self.logger.error(formatted_message)
        if log:
            sentry_sdk.capture_exception(exception)

    def exception(self, exception: Exception, log: bool = True):
        self.logger.exception(exception)
        if log:
            sentry_sdk.capture_exception(exception)
