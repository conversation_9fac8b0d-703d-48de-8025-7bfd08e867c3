from bson import ObjectId

from src.models.project import InsertProjectInput, Project
from src.util.mongo import db


class ProjectRepository:
    def __init__(self):
        self.collection = db.get_collection("projects")

    def _to_model(self, project_dict: dict) -> Project:
        return Project(
            id=str(project_dict.get("_id")),
            organization_id=str(project_dict.get("organization_id")),
            name=project_dict.get("name"),
            slug=project_dict.get("slug"),
            created_at=project_dict.get("created_at"),
        )

    async def find_one(
        self,
        project_id: str | None = None,
        organization_id: str | None = None,
        slug: str | None = None,
    ) -> Project | None:
        filter = {}
        if project_id:
            filter["_id"] = ObjectId(project_id)
        if organization_id:
            filter["organization_id"] = ObjectId(organization_id)
        if slug:
            filter["slug"] = slug
        project_dict = await self.collection.find_one(filter=filter)
        return self._to_model(project_dict) if project_dict else None

    async def insert_one(self, input: InsertProjectInput) -> Project:
        result = await self.collection.insert_one(input.to_dict())
        return await self.find_one(project_id=result.inserted_id)