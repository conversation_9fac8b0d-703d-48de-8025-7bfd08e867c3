from bson import ObjectId
from llama_index.core.node_parser import SimpleNodeParser
from llama_index.core.postprocessor.types import BaseNodePostprocessor
from llama_index.core.schema import NodeWithScore, QueryBundle, TextNode
from llama_index.postprocessor.cohere_rerank import CohereRerank
from pydantic import Field

from src.util.mongo import sync_db


class FullDocumentPostprocessor(BaseNodePostprocessor):
    project_id: str = Field(default=None)
    document_ids: list[str] = Field(default=None)

    def _postprocess_nodes(
        self,
        nodes: list[NodeWithScore],
        query_bundle: QueryBundle | None = None,
    ) -> list[NodeWithScore]:
        if not self.project_id:
            return nodes

        if not self.document_ids:
            for node in nodes:
                if node.node.metadata.get("app_document_id") not in self.document_ids:
                    self.document_ids.append(node.node.metadata.get("app_document_id"))

        documents = (
            sync_db.get_collection("documents")
            .find(
                {
                    "project_id": ObjectId(self.project_id),
                    "_id": {
                        "$in": [
                            ObjectId(document_id) for document_id in self.document_ids
                        ]
                    },
                }
            )
            .to_list()
        )

        for document in documents:
            document_nodes = [
                TextNode().from_dict(node) for node in document.get("nodes")
            ]
            document_nodes = SimpleNodeParser().get_nodes_from_documents(document_nodes)
            nodes_with_score = [
                NodeWithScore(node=node, score=0.0) for node in document_nodes
            ]
            nodes.extend(nodes_with_score)

        return nodes


class EmptyResponsePostprocessor(BaseNodePostprocessor):
    def _postprocess_nodes(
        self,
        nodes: list[NodeWithScore],
        query_bundle: QueryBundle | None = None,
    ) -> list[NodeWithScore]:
        if not nodes:
            nodes.append(
                NodeWithScore(
                    node=TextNode(text="No results found"),
                    score=0.0,
                )
            )
        return nodes


class ScoreNormalizerPostprocessor(BaseNodePostprocessor):
    def _postprocess_nodes(
        self,
        nodes: list[NodeWithScore],
        query_bundle: QueryBundle | None = None,
    ) -> list[NodeWithScore]:
        if not nodes:
            return nodes
        max_score = max([node.score for node in nodes]) + 0.1
        for node in nodes:
            node.score = node.score / max_score
        return nodes


class RerankPostprocessor(BaseNodePostprocessor):
    top_n: float = Field(default=10)

    def _postprocess_nodes(
        self,
        nodes: list[NodeWithScore],
        query_bundle: QueryBundle | None = None,
    ) -> list[NodeWithScore]:
        if not nodes:
            return nodes

        reranked_nodes = CohereRerank(
            top_n=self.top_n,
            model="rerank-v3.5",
        )._postprocess_nodes(nodes, query_bundle)

        max_reranked_score = max([node.score for node in reranked_nodes]) + 0.1

        return reranked_nodes if max_reranked_score > 0.1 else nodes
