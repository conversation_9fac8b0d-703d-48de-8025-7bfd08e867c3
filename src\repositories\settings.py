from bson import ObjectId

from src.models.settings import InsertSettingsInput, Settings, UpdateSettingsInput
from src.util.llm_validation import validate_provider_model_compatibility
from src.util.mongo import db


class SettingsRepository:
    def __init__(self):
        self.collection = db.get_collection("settings")

    def _to_model(self, settings_dict: dict) -> Settings:
        user_id = settings_dict.get("user_id")
        if user_id:
            user_id = str(user_id)

        provider, model = validate_provider_model_compatibility(
            settings_dict.get("llm_provider"), settings_dict.get("llm_model")
        )

        return Settings(
            id=str(settings_dict.get("_id")),
            project_id=str(settings_dict.get("project_id")),
            user_id=user_id,
            llm_provider=provider,
            llm_model=model,
            created_at=settings_dict.get("created_at"),
        )

    async def find_one(
        self,
        settings_id: str | None = None,
        project_id: str | None = None,
        user_id: str | None = None,
    ) -> Settings | None:
        filter = {}
        if settings_id:
            filter["_id"] = ObjectId(settings_id)
        if project_id:
            filter["project_id"] = ObjectId(project_id)
        if user_id:
            filter["user_id"] = ObjectId(user_id)
        settings_dict = await self.collection.find_one(filter=filter)
        return self._to_model(settings_dict) if settings_dict else None

    async def insert_one(self, input: InsertSettingsInput) -> Settings:
        result = await self.collection.insert_one(input.to_dict())
        return await self.find_one(settings_id=result.inserted_id)

    async def update_one(
        self, settings_id: str, input: UpdateSettingsInput
    ) -> Settings | None:
        filter = {"_id": ObjectId(settings_id)}
        update = {}

        if input.llm_provider is not None and input.llm_model is not None:
            provider, model = validate_provider_model_compatibility(
                input.llm_provider, input.llm_model
            )
            update["llm_provider"] = provider
            update["llm_model"] = model
        else:
            current_settings = await self.find_one(settings_id=settings_id)
            if not current_settings:
                return None

            if input.llm_provider is not None:
                provider, model = validate_provider_model_compatibility(
                    input.llm_provider, current_settings.llm_model
                )
                update["llm_provider"] = provider
                update["llm_model"] = model
            elif input.llm_model is not None:
                provider, model = validate_provider_model_compatibility(
                    current_settings.llm_provider, input.llm_model
                )
                update["llm_provider"] = provider
                update["llm_model"] = model

        await self.collection.update_one(
            filter=filter,
            update={"$set": update},
        )
        return await self.find_one(settings_id=settings_id)
