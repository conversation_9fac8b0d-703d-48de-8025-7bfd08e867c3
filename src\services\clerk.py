import os

import aiohttp
from fastapi import HTTPException

from src.models.clerk import (
    ClerkOrgan<PERSON>,
    ClerkOrganizationRole,
    Clerk<PERSON>ser,
    ClerkUserMembership,
)
from src.util.logger import Logger


class ClerkService:
    def __init__(self):
        self._base_url = "https://api.clerk.com/v1"
        self.logger = Logger("ClerkService")

    async def get_user(self, clerk_user_id: str) -> ClerkUser | None:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self._base_url}/users/{clerk_user_id}",
                    headers={
                        "Authorization": f"Bearer {os.environ.get('CLERK_SECRET_KEY')}"
                    },
                ) as response:
                    if response.status != 200:
                        return None
                    result = await response.json()
                    email_addresses = result.get("email_addresses")
                    email = None
                    if email_addresses:
                        for email_address in email_addresses:
                            if email_address.get("id") == result.get(
                                "primary_email_address_id"
                            ):
                                email = email_address.get("email_address")
                                break
                    if not email:
                        raise HTTPException(
                            status_code=404,
                            detail="User email not found",
                        )
                    return ClerkUser(
                        id=result.get("id"),
                        first_name=result.get("first_name"),
                        last_name=result.get("last_name"),
                        email=email,
                    )
        except Exception as e:
            self.logger.error("Error getting clerk user", e)
            return None

    async def get_organization(
        self, clerk_organization_id: str
    ) -> ClerkOrganization | None:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self._base_url}/organizations/{clerk_organization_id}",
                    headers={
                        "Authorization": f"Bearer {os.environ.get('CLERK_SECRET_KEY')}"
                    },
                ) as response:
                    if response.status != 200:
                        return None
                    result = await response.json()
                    return ClerkOrganization(
                        id=result.get("id"),
                        name=result.get("name"),
                        slug=result.get("slug"),
                    )
        except Exception as e:
            self.logger.error("Error getting clerk organization", e)
            return None

    async def get_user_memberships(
        self, clerk_user_id: str
    ) -> list[ClerkUserMembership]:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self._base_url}/users/{clerk_user_id}/organization_memberships",
                    headers={
                        "Authorization": f"Bearer {os.environ.get('CLERK_SECRET_KEY')}"
                    },
                ) as response:
                    if response.status != 200:
                        return None
                    result = await response.json()
                    data = result.get("data")
                    if not data:
                        return []
                    memberships = []
                    for membership in data:
                        organization = membership.get("organization")
                        if organization:
                            memberships.append(
                                ClerkUserMembership(
                                    organization=ClerkOrganization(
                                        id=organization.get("id"),
                                        name=organization.get("name"),
                                        slug=organization.get("slug"),
                                    ),
                                    role=ClerkOrganizationRole.ADMIN
                                    if membership.get("role")
                                    == ClerkOrganizationRole.ADMIN
                                    else ClerkOrganizationRole.MEMBER,
                                )
                            )
                    return memberships
        except Exception as e:
            self.logger.error("Error getting clerk user memberships", e)
            return []
