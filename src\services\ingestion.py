from datetime import datetime
from typing import Sequence

from llama_index.core.extractors import PydanticProgramExtractor
from llama_index.core.ingestion import IngestionPipeline
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.schema import BaseNode
from llama_index.core.schema import Document as ParsedDocument
from llama_index.program.openai import OpenAIPydanticProgram
from llama_parse import LlamaParse
from pydantic import BaseModel, Field

from src.models.document import (
    DOCUMENT_CATEGORIES,
    DOCUMENT_TYPES,
    DocumentCategory,
    DocumentKeywords,
    DocumentType,
    UpdateDocumentInput,
)
from src.models.ingestion import ExtractMetadataInput
from src.rag.config import CHUNK_OVERLAP, CHUNK_SIZE, OPEN_AI_EMBED_MODEL, OPEN_AI_LLM
from src.repositories.document import DocumentRepository
from src.services.retriever import RetrieverService
from src.util.logger import Logger


class DocumentMetadataExtractor(BaseModel):
    """Document Metadata"""

    title: str = Field(
        description="A short readable document name",
    )
    slug: str = Field(
        description="A unique identifier for the document based on the name",
    )
    description: str = Field(
        description="A short 1 line description of the document",
    )
    project_name: str = Field(
        description="The name of the project",
    )
    companies_involved: list[str] = Field(
        description="The names of the companies involved in the document",
    )
    keywords: list[str] = Field(
        description="10 important keywords in the document",
    )
    document_types: list[str] = Field(
        description=f"CRITICAL REQUIREMENT: You MUST ALWAYS return EXACTLY 3 different document types from this list: {', '.join(DOCUMENT_TYPES)}. This is MANDATORY regardless of confidence level. If uncertain, choose the 3 types that could possibly relate to any aspect of the document content. Never return fewer than 3 values or an empty list.",
    )
    document_categories: list[str] = Field(
        description=f"CRITICAL REQUIREMENT: You MUST ALWAYS return EXACTLY 3 different document categories from this list: {', '.join(DOCUMENT_CATEGORIES)}. This is MANDATORY regardless of confidence level. If uncertain, choose the 3 categories that could possibly relate to any aspect of the document content. Never return fewer than 3 values or an empty list.",
    )
    entity_ids: list[str] = Field(
        description="The ids of the entities in the document. It can be numeric, alphanumeric or UUID",
    )
    document_date: str = Field(
        description="The date the document was created or signed, in the format YYYY-MM-DDT00:00:00Z. There can only be one date or none",
    )


class IngestionService:
    def __init__(self):
        self.logger = Logger("IngestionService")
        self.document_repository = DocumentRepository()
        self.retriever_service = RetrieverService()

    def text(self, content: bytes) -> str:
        return content.decode("utf-8")

    async def parse(
        self,
        content: bytes,
        file_name: str,
        fast_mode: bool = False,
    ) -> list[ParsedDocument]:
        parser = LlamaParse(max_timeout=3600, fast_mode=fast_mode)
        return await parser.aload_data(content, extra_info={"file_name": file_name})

    async def ingest(
        self,
        parsed_documents: list[ParsedDocument],
        input: ExtractMetadataInput,
    ) -> Sequence[BaseNode]:
        for document in parsed_documents:
            if input.organization_id:
                document.metadata.update({"app_organization_id": input.organization_id})
            if input.project_id:
                document.metadata.update({"app_project_id": input.project_id})
            if input.user_id:
                document.metadata.update({"app_user_id": input.user_id})
            if input.conversation_id:
                document.metadata.update({"app_conversation_id": input.conversation_id})
            if input.document_id:
                document.metadata.update({"app_document_id": input.document_id})
            if input.context_id:
                document.metadata.update({"app_context_id": input.context_id})
            if input.external_id:
                document.metadata.update({"app_external_id": input.external_id})
            if input.metadata:
                document.metadata.update({"app_metadata": input.metadata})
            if input.file_name:
                document.metadata.update({"app_file_name": input.file_name})
            if input.file_slug:
                document.metadata.update({"app_file_slug": input.file_slug})

        ingestion_pipeline = IngestionPipeline(
            transformations=[
                SentenceSplitter(
                    separator=".",
                    paragraph_separator="\n",
                    chunk_size=CHUNK_SIZE,
                    chunk_overlap=CHUNK_OVERLAP,
                ),
                PydanticProgramExtractor(
                    show_progress=False,
                    program=OpenAIPydanticProgram.from_defaults(
                        output_cls=DocumentMetadataExtractor,
                        prompt_template_str="{input}",
                        llm=OPEN_AI_LLM,
                    ),
                ),
                OPEN_AI_EMBED_MODEL,
            ],
            vector_store=self.retriever_service.get_vector_store(input.organization_id),
        )

        nodes = await ingestion_pipeline.arun(documents=parsed_documents)

        if input.document_id and not input.context_id:
            keywords = DocumentKeywords()

            for node in nodes:
                for document_type in node.metadata.get("document_types"):
                    if (
                        document_type
                        in [document_type.value for document_type in DocumentType]
                        and DocumentType(document_type) not in keywords.document_types
                    ):
                        keywords.document_types.append(DocumentType(document_type))
                for document_category in node.metadata.get("document_categories"):
                    if (
                        document_category
                        in [
                            document_category.value
                            for document_category in DocumentCategory
                        ]
                        and DocumentCategory(document_category)
                        not in keywords.document_categories
                    ):
                        keywords.document_categories.append(
                            DocumentCategory(document_category)
                        )
                for entity_id in node.metadata.get("entity_ids"):
                    if entity_id not in keywords.entity_ids:
                        keywords.entity_ids.append(entity_id)
                if node.metadata.get("document_date"):
                    date = node.metadata.get("document_date")
                    try:
                        keywords.document_date = datetime.strptime(
                            date, "%Y-%m-%dT%H:%M:%SZ"
                        )
                    except ValueError:
                        self.logger.warning(
                            f"Invalid date format found: {date}. Skipping date parsing."
                        )
                        keywords.document_date = None
            try:
                await self.document_repository.update_one(
                    document_id=input.document_id,
                    input=UpdateDocumentInput(
                        keywords=keywords,
                        nodes=nodes,
                    ),
                )
            except Exception as e:
                error_message = "Error updating document keywords"
                if input.document_id:
                    error_message += f" for document {input.document_id}"
                self.logger.error(error_message, e)
                raise e

        return nodes
