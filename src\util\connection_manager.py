import asyncio

from fastapi import WebSocket
from fastapi.websockets import WebSocketState

from src.util.logger import Logger


class ConnectionManager:
    def __init__(self):
        self.connections: dict[str, WebSocket] = {}
        self.logger = Logger("ConnectionManager")

    def _get_key(
        self,
        organization_id: str,
        project_id: str,
        client_id: str,
    ) -> str:
        return f"{organization_id}-{project_id}-{client_id}"

    async def connect(
        self,
        websocket: WebSocket,
        organization_id: str,
        project_id: str,
        client_id: str,
    ) -> bool:
        try:
            await websocket.accept()
            key = self._get_key(organization_id, project_id, client_id)
            self.connections[key] = websocket
            asyncio.create_task(self.check_connections())
            return True
        except Exception as e:
            self.logger.error("Error connecting client", e)
            return False

    async def disconnect(
        self,
        organization_id: str,
        project_id: str,
        client_id: str,
    ) -> bool:
        try:
            key = self._get_key(organization_id, project_id, client_id)
            connection = self.connections.pop(key, None)
            if connection:
                await self.close(connection)
                return True
        except Exception as e:
            self.logger.error("Error disconnecting client", e)
            return False

    async def close(self, websocket: WebSocket) -> None:
        try:
            await websocket.close()
        except Exception:
            pass

    async def check_connections(self) -> None:
        tasks = []
        for key, connection in self.connections.items():
            if connection.client_state == WebSocketState.DISCONNECTED:
                tasks.append(self.disconnect(key))
        await asyncio.gather(*tasks)
