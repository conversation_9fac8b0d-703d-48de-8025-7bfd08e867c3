from fastapi import APIRouter, Request

from src.models.message import MessageUsageResponse, UserQuery
from src.models.shared import PaginationResponse
from src.services.message import MessageService

message_router = APIRouter()
message_service = MessageService()


@message_router.get("/paginate", response_model=PaginationResponse[UserQuery])
async def paginate(
    request: Request,
    user_id: str | None = None,
    page_size: int = 10,
    page_number: int = 1,
):
    return await message_service.paginate_user_queries(
        auth=request.state.auth,
        user_id=user_id,
        page_size=page_size,
        page_number=page_number,
    )


@message_router.get("/usage", response_model=MessageUsageResponse)
async def usage(request: Request, user_id: str | None = None):
    return await message_service.usage(
        auth=request.state.auth,
        user_id=user_id,
    )
