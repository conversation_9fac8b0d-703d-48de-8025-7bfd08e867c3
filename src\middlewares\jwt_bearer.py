from fastapi import <PERSON>TT<PERSON><PERSON><PERSON><PERSON>, Request, Security
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from src.services.auth import AuthService


auth_service = AuthService()


async def validate_token(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Security(HTTPBearer()),
):
    if not credentials or not credentials.scheme == "Bearer":
        raise HTTPException(status_code=403, detail="Invalid authentication scheme.")
    token = credentials.credentials
    auth = await auth_service.authenticate_by_token(token)
    if not auth:
        raise HTTPException(status_code=403, detail="Invalid or expired token.")
    request.state.auth = auth
    return token
