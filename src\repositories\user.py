from bson import ObjectId
from pymongo import ReturnDocument

from src.models.user import InsertUserInput, UpdateUserInput, User
from src.util.mongo import db


class UserRepository:
    def __init__(self):
        self.collection = db.get_collection("users")

    def _to_model(self, user_dict: dict) -> User:
        return User(
            id=str(user_dict.get("_id")),
            clerk_user_id=user_dict.get("clerk_user_id"),
            clerk=None,
            accepted_terms=user_dict.get("accepted_terms"),
            created_at=user_dict.get("created_at"),
        )

    async def find_one(
        self,
        user_id: str | None = None,
        clerk_user_id: str | None = None,
    ) -> User | None:
        filter = {}
        if user_id:
            filter["_id"] = ObjectId(user_id)
        if clerk_user_id:
            filter["clerk_user_id"] = clerk_user_id
        user_dict = await self.collection.find_one(filter=filter)
        return self._to_model(user_dict) if user_dict else None

    async def upsert_one(self, input: InsertUserInput) -> User:
        user_dict = await self.collection.find_one_and_update(
            {"clerk_user_id": input.clerk_user_id},
            {"$setOnInsert": input.to_dict()},
            upsert=True,
            return_document=ReturnDocument.AFTER,
        )
        return self._to_model(user_dict)

    async def update_one(self, user_id: str, input: UpdateUserInput) -> User | None:
        filter = {"_id": ObjectId(user_id)}
        update = {}
        if input.accepted_terms is not None:
            update["accepted_terms"] = input.accepted_terms
        await self.collection.update_one(filter=filter, update={"$set": update})
        return await self.find_one(user_id=user_id)
